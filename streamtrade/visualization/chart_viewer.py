"""
Chart viewer for StreamTrade platform.
Integrates data management, indicators, and visualization.
Enhanced with impact-based updates for Phase 5.5.
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import plotly.graph_objects as go

from ..data.enhanced_data_manager import EnhancedDataManager
from ..indicators.indicator_manager import IndicatorManager
from ..cache.impact_manager import ChangeType
from ..cache.coherency_manager import get_coherency_manager
from .plotly_charts import PlotlyCharts
from ..config.logging_config import get_logger
from ..config.settings import settings

logger = get_logger(__name__)


class ChartViewer:
    """
    Main chart viewer that integrates data, indicators, and visualization.
    
    Features:
    - Load and display OHLCV data
    - Apply multiple technical indicators
    - Interactive chart with zoom/pan
    - Dynamic timeframe switching
    - Indicator management
    - Export capabilities
    """
    
    def __init__(self):
        self.data_manager = EnhancedDataManager()
        self.indicator_manager = IndicatorManager(data_manager=self.data_manager)
        self.plotly_charts = PlotlyCharts()

        # Current chart state
        self.current_data = None
        self.current_pair = None
        self.current_timeframe = None
        self.current_indicators = {}
        self.current_data_cache_key = None  # Track current data cache key

        logger.info("ChartViewer initialized with EnhancedDataManager and caching support")

    def save_indicator_state(self):
        """Save current indicator configuration to both session state and persistent file."""
        try:
            if hasattr(self, 'indicator_manager') and self.indicator_manager.indicators:
                import json
                from pathlib import Path
                from datetime import datetime

                # Prepare indicator state data
                indicator_state = {
                    'indicators': {name: {
                        'indicator_type': config.indicator_type,
                        'parameters': config.parameters,
                        'enabled': config.enabled,
                        'display_name': config.display_name
                    } for name, config in self.indicator_manager.indicators.items()},
                    'pair': self.current_pair,
                    'timeframe': self.current_timeframe,
                    'timestamp': datetime.now().isoformat()
                }

                # Save to session state (for current session) - only if Streamlit context available
                try:
                    import streamlit as st
                    st.session_state['chart_indicators'] = indicator_state
                    logger.debug(f"Saved {len(self.indicator_manager.indicators)} indicators to session state")
                except Exception as st_error:
                    logger.debug(f"Streamlit context not available, skipping session state: {st_error}")

                # Save to persistent file (for browser refresh) - always try this
                config_dir = Path(__file__).parent.parent / 'config'
                config_dir.mkdir(exist_ok=True)
                indicator_file = config_dir / 'last_indicator_state.json'

                with open(indicator_file, 'w', encoding='utf-8') as f:
                    json.dump(indicator_state, f, indent=2)

                logger.debug(f"Saved {len(self.indicator_manager.indicators)} indicators to persistent file: {indicator_file}")
        except Exception as e:
            logger.error(f"Error saving indicator state: {e}")

    def load_indicator_state(self):
        """Load indicator configuration from session state or persistent file."""
        try:
            import json
            from pathlib import Path

            saved_state = None
            source = "none"

            # Try session state first (current session) - only if Streamlit context available
            try:
                import streamlit as st
                if 'chart_indicators' in st.session_state:
                    saved_state = st.session_state['chart_indicators']
                    source = "session_state"
            except Exception as st_error:
                logger.debug(f"Streamlit context not available, skipping session state: {st_error}")

            # Try persistent file if no session state (browser refresh or no Streamlit context)
            if not saved_state:
                config_dir = Path(__file__).parent.parent / 'config'
                indicator_file = config_dir / 'last_indicator_state.json'

                if indicator_file.exists():
                    try:
                        with open(indicator_file, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                        if content:
                            saved_state = json.loads(content)
                            source = "persistent_file"
                    except Exception as e:
                        logger.warning(f"Failed to load persistent indicator state: {e}")

            if saved_state:
                # Only restore if same pair/timeframe
                if (saved_state.get('pair') == self.current_pair and
                    saved_state.get('timeframe') == self.current_timeframe):

                    indicators_data = saved_state.get('indicators', {})
                    restored_count = 0

                    for name, config in indicators_data.items():
                        success = self.indicator_manager.add_indicator(
                            name=name,
                            indicator_type=config['indicator_type'],
                            parameters=config['parameters'],
                            display_name=config.get('display_name')
                        )
                        if success:
                            if not config.get('enabled', True):
                                self.indicator_manager.disable_indicator(name)
                            restored_count += 1

                    if restored_count > 0:
                        logger.info(f"Restored {restored_count} indicators from {source}")
                        return True
                else:
                    logger.debug(f"Indicator state found but pair/timeframe mismatch: {saved_state.get('pair')}/{saved_state.get('timeframe')} vs {self.current_pair}/{self.current_timeframe}")

        except Exception as e:
            logger.error(f"Error loading indicator state: {e}")

        return False

    def load_data(
        self,
        pair: str,
        timeframe: str = "H1",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        max_candles: Optional[int] = None
    ) -> bool:
        """
        Load data for chart display.
        
        Args:
            pair: Currency pair
            timeframe: Timeframe
            start_date: Start date (optional)
            end_date: End date (optional)
            max_candles: Maximum number of candles
            
        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading data: {pair} {timeframe}")
            
            # Use default max_candles if not specified
            if max_candles is None:
                max_candles = settings.chart_settings["max_candles_display"]
            
            # Load data using enhanced data manager
            if start_date and end_date:
                data = self.data_manager.load_data_range(pair, timeframe, start_date, end_date)
            elif max_candles:
                # Convert max_candles to days_back for compatibility
                days_back = max(1, max_candles // 24)  # Rough conversion
                data = self.data_manager.load_n_days_back(pair, timeframe, days_back)
            else:
                # Default to 5 days back
                data = self.data_manager.load_n_days_back(pair, timeframe, 5)
            
            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe}")
                return False
            
            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe

            # Generate and set data cache key for indicator caching
            self.current_data_cache_key = f"data_{pair}_{timeframe}_{len(data)}"
            self.indicator_manager.set_data_cache_key(self.current_data_cache_key)

            # Clear previous indicator results
            self.current_indicators = {}

            # Try to restore indicators from session state
            if self.load_indicator_state():
                # Recalculate indicators if restored
                self._calculate_indicators()

            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return False

    def load_data_n_days_back(self, pair: str, timeframe: str, days_back: int) -> bool:
        """
        Load data using N Days Back strategy.

        Args:
            pair: Currency pair
            timeframe: Timeframe
            days_back: Number of days to load back

        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading {days_back} days back: {pair} {timeframe}")

            data = self.data_manager.load_n_days_back(pair, timeframe, days_back)

            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe} ({days_back} days back)")
                return False

            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe

            # Generate and set data cache key for indicator caching
            self.current_data_cache_key = f"data_{pair}_{timeframe}_{len(data)}"
            self.indicator_manager.set_data_cache_key(self.current_data_cache_key)

            # Clear previous indicator results
            self.current_indicators = {}

            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe} ({days_back} days back)")
            return True

        except Exception as e:
            logger.error(f"Error loading data N days back: {str(e)}")
            return False

    def load_data_range(self, pair: str, timeframe: str, start_date: datetime, end_date: datetime) -> bool:
        """
        Load data for specific date range.

        Args:
            pair: Currency pair
            timeframe: Timeframe
            start_date: Start date
            end_date: End date

        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading date range: {pair} {timeframe} ({start_date.date()} to {end_date.date()})")

            data = self.data_manager.load_data_range(pair, timeframe, start_date, end_date)

            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe} in date range")
                return False

            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe

            # Clear previous indicator results
            self.current_indicators = {}

            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe} (date range)")
            return True

        except Exception as e:
            logger.error(f"Error loading data range: {str(e)}")
            return False

    def load_all_available_data(self, pair: str, timeframe: str) -> bool:
        """
        Load all available data for pair and timeframe.

        Args:
            pair: Currency pair
            timeframe: Timeframe

        Returns:
            True if data loaded successfully, False otherwise
        """
        try:
            logger.info(f"Loading all available data: {pair} {timeframe}")

            # Use a large days_back value to get all available data
            data = self.data_manager.load_n_days_back(pair, timeframe, 365)  # 1 year max

            if data is None or data.empty:
                logger.error(f"No data available for {pair} {timeframe}")
                return False

            # Update current state
            self.current_data = data
            self.current_pair = pair
            self.current_timeframe = timeframe

            # Clear previous indicator results
            self.current_indicators = {}

            logger.info(f"Loaded {len(data)} candles for {pair} {timeframe} (all available)")
            return True

        except Exception as e:
            logger.error(f"Error loading all available data: {str(e)}")
            return False
    
    def add_indicator(
        self,
        name: str,
        indicator_type: str,
        parameters: Optional[Dict[str, Any]] = None,
        display_name: Optional[str] = None
    ) -> bool:
        """
        Add an indicator to the chart.
        
        Args:
            name: Unique name for indicator instance
            indicator_type: Type of indicator
            parameters: Indicator parameters
            display_name: Display name for UI
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            success = self.indicator_manager.add_indicator(
                name=name,
                indicator_type=indicator_type,
                parameters=parameters,
                display_name=display_name
            )
            
            if success:
                logger.info(f"Added indicator: {name} ({indicator_type})")
                # Recalculate indicators if data is available
                if self.current_data is not None:
                    self._calculate_indicators()
            
            return success
            
        except Exception as e:
            logger.error(f"Error adding indicator: {str(e)}")
            return False
    
    def remove_indicator(self, name: str) -> bool:
        """Remove an indicator from the chart."""
        try:
            success = self.indicator_manager.remove_indicator(name)
            
            if success:
                # Remove from current indicators
                if name in self.current_indicators:
                    del self.current_indicators[name]
                
                logger.info(f"Removed indicator: {name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error removing indicator: {str(e)}")
            return False
    
    def update_indicator_parameters(
        self,
        name: str,
        parameters: Dict[str, Any]
    ) -> bool:
        """Update parameters for an existing indicator with impact-based optimization."""
        try:
            success = self.indicator_manager.update_indicator_parameters(name, parameters)

            if success and self.current_data is not None:
                # Phase 5.5: Use impact-based recalculation for parameter changes
                self._calculate_indicators(
                    change_type=ChangeType.INDICATOR_PARAM,
                    affected_indicators={name}
                )
                logger.info(f"Updated indicator parameters with impact-based calculation: {name}")

            return success

        except Exception as e:
            logger.error(f"Error updating indicator parameters: {str(e)}")
            return False
    
    def toggle_indicator(self, name: str, enabled: bool) -> bool:
        """Enable or disable an indicator with impact-based optimization."""
        try:
            if enabled:
                success = self.indicator_manager.enable_indicator(name)
            else:
                success = self.indicator_manager.disable_indicator(name)

            if success and self.current_data is not None:
                # Phase 5.5: Use impact-based recalculation for toggle changes
                self._calculate_indicators(
                    change_type=ChangeType.INDICATOR_TOGGLE,
                    affected_indicators={name}
                )

            return success

        except Exception as e:
            logger.error(f"Error toggling indicator: {str(e)}")
            return False
    
    def _calculate_indicators(self, change_type: ChangeType = None, affected_indicators: set = None):
        """
        Calculate indicators for current data with optional impact-based optimization.

        Args:
            change_type: Type of change that triggered this calculation
            affected_indicators: Set of indicators affected by the change
        """
        try:
            if self.current_data is None:
                return

            # Phase 5.5: Use impact-based calculation if change information is provided
            if change_type is not None:
                self.current_indicators = self.indicator_manager.calculate_with_impact_analysis(
                    self.current_data, change_type, affected_indicators
                )
                logger.debug(f"Impact-based calculation completed for {len(self.current_indicators)} indicators")
            else:
                # Fallback to calculating all indicators
                self.current_indicators = self.indicator_manager.calculate_all(self.current_data)
                logger.debug(f"Calculated {len(self.current_indicators)} indicators")

            # Save indicator state after calculation
            self.save_indicator_state()

        except Exception as e:
            logger.error(f"Error calculating indicators: {str(e)}")
    
    def create_chart(
        self,
        title: Optional[str] = None,
        height: Optional[int] = None,
        width: Optional[int] = None,
        remove_gaps: bool = True,
        chart_style: str = "Candlestick",
        show_vertical_line: bool = True,
        show_horizontal_line: bool = True
    ) -> go.Figure:
        """
        Create chart with current data and indicators.

        Args:
            title: Chart title (auto-generated if None)
            height: Chart height
            width: Chart width
            remove_gaps: Whether to remove weekend gaps
            chart_style: Chart style (Candlestick, OHLC Bars, Line)
            show_vertical_line: Show vertical crosshair line
            show_horizontal_line: Show horizontal crosshair line

        Returns:
            Plotly Figure object
        """
        try:
            if self.current_data is None:
                logger.warning("No data loaded for chart creation")
                return self._create_empty_chart()
            
            # Generate title if not provided
            if title is None:
                title = f"{self.current_pair} - {self.current_timeframe}"
                if len(self.current_data) > 0:
                    start_date = self.current_data.index[0].strftime("%Y-%m-%d")
                    end_date = self.current_data.index[-1].strftime("%Y-%m-%d")
                    title += f" ({start_date} to {end_date})"
            
            # Calculate indicators if not already done
            if not self.current_indicators and len(self.indicator_manager.indicators) > 0:
                self._calculate_indicators()
            
            # Create chart
            fig = self.plotly_charts.create_candlestick_chart(
                data=self.current_data,
                indicators=self.current_indicators,
                title=title,
                height=height,
                width=width,
                remove_gaps=remove_gaps,
                chart_style=chart_style,
                show_vertical_line=show_vertical_line,
                show_horizontal_line=show_horizontal_line
            )
            
            logger.debug(f"Created chart with {len(self.current_data)} candles and {len(self.current_indicators)} indicators")
            return fig
            
        except Exception as e:
            logger.error(f"Error creating chart: {str(e)}")
            return self._create_error_chart(str(e))
    
    def refresh_chart(self) -> go.Figure:
        """Refresh chart with current settings."""
        try:
            if self.current_pair and self.current_timeframe:
                # Reload data
                self.load_data(self.current_pair, self.current_timeframe)
                
                # Create new chart
                return self.create_chart()
            else:
                logger.warning("No current data to refresh")
                return self._create_empty_chart()
                
        except Exception as e:
            logger.error(f"Error refreshing chart: {str(e)}")
            return self._create_error_chart(str(e))
    
    def change_timeframe(self, new_timeframe: str) -> go.Figure:
        """
        Change timeframe and update chart (optimized for speed).

        Args:
            new_timeframe: New timeframe to display

        Returns:
            Updated chart figure
        """
        try:
            if not self.current_pair:
                logger.warning("No pair loaded for timeframe change")
                return self._create_empty_chart()

            logger.info(f"Switching timeframe: {self.current_timeframe} → {new_timeframe}")

            # Store current indicators configuration for re-application
            current_indicators_config = {}
            if self.current_indicators:
                for name, result in self.current_indicators.items():
                    indicator_info = self.indicator_manager.get_indicator_info(name)
                    if indicator_info:
                        current_indicators_config[name] = {
                            'type': indicator_info['name'],
                            'parameters': indicator_info['current_parameters'],
                            'enabled': indicator_info['enabled']
                        }

            # Use optimized timeframe switching
            data = self.data_manager.switch_timeframe(
                pair=self.current_pair,
                new_timeframe=new_timeframe
            )

            if data is None or data.empty:
                logger.error(f"No data available for {self.current_pair} {new_timeframe}")
                return self._create_error_chart(f"No data available for {new_timeframe}")

            # Update current state
            self.current_data = data
            self.current_timeframe = new_timeframe

            # Clear previous indicator results
            self.current_indicators = {}

            # Re-apply indicators if they existed
            if current_indicators_config:
                logger.debug(f"Re-applying {len(current_indicators_config)} indicators")
                self._calculate_indicators()

            # Create chart with new timeframe data
            logger.info(f"Successfully switched to {new_timeframe}: {len(data)} candles")
            return self.create_chart()

        except Exception as e:
            logger.error(f"Error changing timeframe: {str(e)}")
            return self._create_error_chart(str(e))

    def get_data_info(self) -> Dict[str, Any]:
        """Get information about current data and user context."""
        info = {
            'pair': self.current_pair,
            'timeframe': self.current_timeframe,
            'candles_loaded': len(self.current_data) if self.current_data is not None else 0,
            'user_context': None,
            'loading_limits': {
                'max_candles_load': self.data_manager.max_candles_load,
                'max_candles_display': self.data_manager.max_candles_display
            }
        }

        if self.current_pair:
            user_context = self.data_manager.get_user_context(self.current_pair)
            if user_context:
                info['user_context'] = {
                    'last_timeframe': user_context.get('last_timeframe'),
                    'last_days_back': user_context.get('last_days_back')
                }

        return info

    def get_chart_info(self) -> Dict[str, Any]:
        """Get information about current chart state."""
        return {
            'pair': self.current_pair,
            'timeframe': self.current_timeframe,
            'data_points': len(self.current_data) if self.current_data is not None else 0,
            'date_range': {
                'start': self.current_data.index[0] if self.current_data is not None and len(self.current_data) > 0 else None,
                'end': self.current_data.index[-1] if self.current_data is not None and len(self.current_data) > 0 else None
            },
            'indicators': list(self.current_indicators.keys()),
            'indicator_count': len(self.current_indicators)
        }

    def get_cache_info(self) -> Dict[str, Any]:
        """Get comprehensive cache information."""
        try:
            cache_info = {
                'disk_cache_entries': 0,
                'memory_cache_entries': 0,
                'total_cache_size_mb': 0.0,
                'disk_cache_size_mb': 0.0,
                'memory_cache_size_mb': 0.0
            }

            # Get disk cache info
            if hasattr(self.data_manager, 'disk_cache') and self.data_manager.disk_cache:
                disk_stats = self.data_manager.disk_cache.get_cache_stats()
                cache_info['disk_cache_entries'] = disk_stats.get('total_entries', 0)
                cache_info['disk_cache_size_mb'] = disk_stats.get('total_size_mb', 0.0)

            # Get memory cache info
            if hasattr(self.data_manager, '_data_cache'):
                cache_info['memory_cache_entries'] = len(self.data_manager._data_cache)
                # Estimate memory cache size (rough calculation)
                cache_info['memory_cache_size_mb'] = cache_info['memory_cache_entries'] * 0.5  # Rough estimate

            # Calculate total
            cache_info['total_cache_size_mb'] = cache_info['disk_cache_size_mb'] + cache_info['memory_cache_size_mb']

            return cache_info

        except Exception as e:
            logger.error(f"Error getting cache info: {e}")
            return {
                'disk_cache_entries': 0,
                'memory_cache_entries': 0,
                'total_cache_size_mb': 0.0,
                'disk_cache_size_mb': 0.0,
                'memory_cache_size_mb': 0.0
            }

    def clear_all_cache(self) -> bool:
        """Clear all cache data (disk and memory)."""
        try:
            success = True

            # Clear disk cache
            if hasattr(self.data_manager, 'disk_cache') and self.data_manager.disk_cache:
                try:
                    self.data_manager.disk_cache.clear_all_cache()
                    logger.info("Disk cache cleared successfully")
                except Exception as e:
                    logger.error(f"Error clearing disk cache: {e}")
                    success = False

            # Clear memory cache
            if hasattr(self.data_manager, '_data_cache'):
                try:
                    self.data_manager._data_cache.clear()
                    logger.info("Memory cache cleared successfully")
                except Exception as e:
                    logger.error(f"Error clearing memory cache: {e}")
                    success = False

            # Clear M1 base cache
            if hasattr(self.data_manager, '_m1_base_cache'):
                try:
                    self.data_manager._m1_base_cache.clear()
                    logger.info("M1 base cache cleared successfully")
                except Exception as e:
                    logger.error(f"Error clearing M1 base cache: {e}")
                    success = False

            # Clear session converter cache
            if hasattr(self.data_manager, 'session_converter'):
                try:
                    self.data_manager.session_converter.clear_cache()
                    logger.info("Session converter cache cleared successfully")
                except Exception as e:
                    logger.error(f"Error clearing session converter cache: {e}")
                    success = False

            # Clear data loader cache
            if hasattr(self.data_manager, 'data_loader'):
                try:
                    self.data_manager.data_loader.clear_cache()
                    logger.info("Data loader cache cleared successfully")
                except Exception as e:
                    logger.error(f"Error clearing data loader cache: {e}")
                    success = False

            if success:
                logger.info("All cache cleared successfully")
            else:
                logger.warning("Some cache clearing operations failed")

            return success

        except Exception as e:
            logger.error(f"Error clearing all cache: {e}")
            return False
    
    def get_available_pairs(self) -> List[str]:
        """Get list of available currency pairs."""
        return self.data_manager.get_available_pairs()

    def get_available_timeframes(self) -> List[str]:
        """Get list of available timeframes."""
        return self.data_manager.get_available_timeframes()
    
    def get_available_indicators(self) -> Dict[str, str]:
        """Get list of available indicators."""
        return self.indicator_manager.get_available_indicators()
    
    def get_indicators_by_category(self) -> Dict[str, List[str]]:
        """Get indicators grouped by category."""
        return self.indicator_manager.get_indicators_by_category()
    
    def get_indicator_info(self, name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific indicator."""
        return self.indicator_manager.get_indicator_info(name)
    
    def export_indicator_config(self) -> str:
        """Export current indicator configuration."""
        return self.indicator_manager.export_configuration()
    
    def import_indicator_config(self, config_json: str) -> bool:
        """Import indicator configuration."""
        success = self.indicator_manager.import_configuration(config_json)
        
        if success and self.current_data is not None:
            self._calculate_indicators()
        
        return success

    # ===== COORDINATED CACHE OPERATIONS (Phase 5.5.4) =====

    def load_data_coordinated(self, pair: str, timeframe: str, days_back: int = None) -> bool:
        """
        Load data with coordinated cache operations.

        Args:
            pair: Currency pair
            timeframe: Timeframe
            days_back: Number of days back to load

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get current indicator configurations
            current_indicators = {}
            if hasattr(self, 'indicator_manager') and self.indicator_manager:
                current_indicators = self.indicator_manager.get_all_indicators_info()

            # Coordinate data reload operation
            operation_id = self.data_manager.coordinate_data_reload(
                pair, timeframe, current_indicators
            )

            # Perform actual data loading
            success = self.load_data(pair, timeframe, days_back)

            if success:
                logger.info(f"Coordinated data loading completed: {operation_id}")
            else:
                logger.warning(f"Coordinated data loading failed: {operation_id}")

            return success

        except Exception as e:
            logger.error(f"Error in coordinated data loading: {e}")
            return False

    def switch_timeframe_coordinated(self, new_timeframe: str) -> bool:
        """
        Switch timeframe with coordinated cache operations.

        Args:
            new_timeframe: New timeframe to switch to

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.current_pair or not self.current_timeframe:
                logger.warning("No current data for coordinated timeframe switch")
                return False

            old_timeframe = self.current_timeframe

            # Get current indicator configurations
            current_indicators = {}
            if hasattr(self, 'indicator_manager') and self.indicator_manager:
                current_indicators = self.indicator_manager.get_all_indicators_info()

            # Coordinate timeframe switch operation
            operation_id = self.data_manager.coordinate_timeframe_switch(
                self.current_pair, old_timeframe, new_timeframe, current_indicators
            )

            # Perform actual timeframe switch
            success = self.load_data(self.current_pair, new_timeframe)

            if success:
                logger.info(f"Coordinated timeframe switch completed: {operation_id}")
            else:
                logger.warning(f"Coordinated timeframe switch failed: {operation_id}")

            return success

        except Exception as e:
            logger.error(f"Error in coordinated timeframe switch: {e}")
            return False

    def get_cache_performance_status(self) -> Dict[str, Any]:
        """Get cache performance status and metrics."""
        try:
            return self.data_manager.get_cache_coherency_status()
        except Exception as e:
            logger.error(f"Error getting cache performance status: {e}")
            return {'error': str(e)}

    def optimize_chart_performance(self) -> Dict[str, Any]:
        """Optimize chart and cache performance."""
        try:
            return self.data_manager.optimize_cache_performance()
        except Exception as e:
            logger.error(f"Error optimizing chart performance: {e}")
            return {'error': str(e)}

    def _create_empty_chart(self) -> go.Figure:
        """Create empty chart placeholder."""
        fig = go.Figure()
        
        fig.add_annotation(
            text="No data loaded<br>Please select a currency pair and timeframe",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        
        fig.update_layout(
            title="StreamTrade Chart",
            height=600,
            width=1000,
            template=settings.chart_settings["theme"]
        )
        
        return fig
    
    def _create_error_chart(self, error_message: str) -> go.Figure:
        """Create error chart."""
        return self.plotly_charts._create_error_chart(error_message)
