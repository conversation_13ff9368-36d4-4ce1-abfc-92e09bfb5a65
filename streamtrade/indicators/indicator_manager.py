"""
Indicator manager for StreamTrade platform.
Enhanced with impact-based updates for Phase 5.5.
"""

import pandas as pd
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
import json

from .technical_indicators import TechnicalIndicators
from .base_indicator import BaseIndicator, IndicatorResult
from ..cache.impact_manager import get_impact_manager, ChangeType
from ..cache.dependency_manager import get_dependency_manager, DependencyType
from ..config.logging_config import get_logger
from ..config.settings import settings

logger = get_logger(__name__)


@dataclass
class IndicatorConfig:
    """Configuration for an indicator instance."""
    name: str
    indicator_type: str
    parameters: Dict[str, Any]
    enabled: bool = True
    display_name: Optional[str] = None


class IndicatorManager:
    """
    Manages multiple indicators for chart display.
    
    Features:
    - Add/remove indicators dynamically
    - Calculate multiple indicators efficiently
    - Manage indicator configurations
    - Export/import indicator setups
    """
    
    def __init__(self, data_manager=None):
        self.indicators: Dict[str, IndicatorConfig] = {}
        self.results: Dict[str, IndicatorResult] = {}
        self.max_indicators = settings.indicator_settings["max_indicators"]
        self.data_manager = data_manager  # For caching support
        self.current_data_cache_key = None  # Track current data cache key

        # Phase 5.5: Impact-based updates and dependency management
        self.impact_manager = get_impact_manager()
        self.dependency_manager = get_dependency_manager()

        logger.info("IndicatorManager initialized with impact-based updates and dependency management")

    def set_data_cache_key(self, data_cache_key: str):
        """Set the current data cache key for indicator caching."""
        self.current_data_cache_key = data_cache_key
        logger.debug(f"Data cache key set: {data_cache_key}")

    def add_indicator(
        self,
        name: str,
        indicator_type: str,
        parameters: Optional[Dict[str, Any]] = None,
        display_name: Optional[str] = None
    ) -> bool:
        """
        Add an indicator to the manager.
        
        Args:
            name: Unique name for this indicator instance
            indicator_type: Type of indicator (SMA, RSI, etc.)
            parameters: Indicator parameters
            display_name: Display name for UI
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            # Check if we've reached the maximum number of indicators
            if len(self.indicators) >= self.max_indicators:
                logger.warning(f"Maximum number of indicators ({self.max_indicators}) reached")
                return False
            
            # Check if name already exists
            if name in self.indicators:
                logger.warning(f"Indicator with name '{name}' already exists")
                return False
            
            # Validate indicator type
            try:
                indicator = TechnicalIndicators.get_indicator(indicator_type)
            except ValueError as e:
                logger.error(f"Invalid indicator type: {str(e)}")
                return False
            
            # Use default parameters if none provided
            if parameters is None:
                parameters = {}
            
            # Validate parameters
            try:
                indicator.validate_parameters(**parameters)
            except ValueError as e:
                logger.error(f"Invalid parameters for {indicator_type}: {str(e)}")
                return False
            
            # Create indicator configuration
            config = IndicatorConfig(
                name=name,
                indicator_type=indicator_type,
                parameters=parameters,
                enabled=True,
                display_name=display_name or f"{indicator_type}_{name}"
            )
            
            self.indicators[name] = config

            # Phase 5.5: Register change for impact-based updates
            self.impact_manager.register_change(
                change_type=ChangeType.INDICATOR_ADD,
                affected_indicators={name},
                metadata={'indicator_type': indicator_type, 'parameters': parameters}
            )

            logger.info(f"Added indicator: {name} ({indicator_type})")
            return True
            
        except Exception as e:
            logger.error(f"Error adding indicator {name}: {str(e)}")
            return False
    
    def remove_indicator(self, name: str) -> bool:
        """
        Remove an indicator from the manager.
        
        Args:
            name: Name of indicator to remove
            
        Returns:
            True if removed successfully, False otherwise
        """
        try:
            if name not in self.indicators:
                logger.warning(f"Indicator '{name}' not found")
                return False
            
            # Get indicator info before deletion
            indicator_type = self.indicators[name].indicator_type

            del self.indicators[name]

            # Also remove results if they exist
            if name in self.results:
                del self.results[name]

            # Phase 5.5: Register change for impact-based updates
            self.impact_manager.register_change(
                change_type=ChangeType.INDICATOR_REMOVE,
                affected_indicators={name},
                metadata={'indicator_type': indicator_type}
            )

            logger.info(f"Removed indicator: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing indicator {name}: {str(e)}")
            return False
    
    def update_indicator_parameters(
        self,
        name: str,
        parameters: Dict[str, Any]
    ) -> bool:
        """
        Update parameters for an existing indicator.
        
        Args:
            name: Name of indicator to update
            parameters: New parameters
            
        Returns:
            True if updated successfully, False otherwise
        """
        try:
            if name not in self.indicators:
                logger.warning(f"Indicator '{name}' not found")
                return False
            
            config = self.indicators[name]
            
            # Validate new parameters
            indicator = TechnicalIndicators.get_indicator(config.indicator_type)
            indicator.validate_parameters(**parameters)
            
            # Update parameters
            old_parameters = config.parameters.copy()
            config.parameters = parameters

            # Clear cached results since parameters changed
            if name in self.results:
                del self.results[name]

            # Phase 5.5: Register change for impact-based updates
            self.impact_manager.register_change(
                change_type=ChangeType.INDICATOR_PARAM,
                affected_indicators={name},
                metadata={
                    'indicator_type': config.indicator_type,
                    'old_parameters': old_parameters,
                    'new_parameters': parameters
                }
            )

            logger.info(f"Updated parameters for indicator: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating indicator {name}: {str(e)}")
            return False
    
    def enable_indicator(self, name: str) -> bool:
        """Enable an indicator."""
        if name in self.indicators:
            self.indicators[name].enabled = True
            logger.debug(f"Enabled indicator: {name}")
            return True
        return False
    
    def disable_indicator(self, name: str) -> bool:
        """Disable an indicator."""
        if name in self.indicators:
            self.indicators[name].enabled = False
            logger.debug(f"Disabled indicator: {name}")
            return True
        return False
    
    def calculate_all(self, data: pd.DataFrame) -> Dict[str, IndicatorResult]:
        """
        Calculate all enabled indicators with caching support.

        Args:
            data: OHLCV DataFrame

        Returns:
            Dictionary of indicator results
        """
        try:
            results = {}
            cache_hits = 0
            cache_misses = 0

            for name, config in self.indicators.items():
                if not config.enabled:
                    continue

                try:
                    # Try to load from cache first
                    cached_result = None
                    if self.data_manager and self.current_data_cache_key:
                        cached_data = self.data_manager.load_indicator_calculation(
                            config.indicator_type, config.parameters, self.current_data_cache_key
                        )
                        if cached_data is not None:
                            # Get indicator instance to retrieve metadata
                            indicator = TechnicalIndicators.get_indicator(config.indicator_type)

                            # Convert cached data back to IndicatorResult with proper metadata
                            cached_result = IndicatorResult(
                                name=config.indicator_type,
                                data=cached_data.to_dict('series'),
                                parameters=config.parameters,
                                metadata={
                                    'calculation_success': True,
                                    'from_cache': True,
                                    'description': indicator.description,
                                    'category': indicator.category,
                                    'data_points': len(cached_data)
                                }
                            )
                            cache_hits += 1
                            logger.debug(f"Loaded indicator from cache: {name}")

                    if cached_result is not None:
                        results[name] = cached_result
                    else:
                        # Calculate indicator
                        indicator = TechnicalIndicators.get_indicator(config.indicator_type)
                        result = indicator.calculate(data, **config.parameters)

                        if result.metadata.get('calculation_success', False):
                            results[name] = result
                            cache_misses += 1

                            # Cache the result
                            if self.data_manager and self.current_data_cache_key:
                                # Convert result data to DataFrame for caching
                                result_df = pd.DataFrame(result.data)
                                self.data_manager.cache_indicator_calculation(
                                    config.indicator_type, config.parameters,
                                    self.current_data_cache_key, result_df
                                )

                            logger.debug(f"Calculated and cached indicator: {name}")
                        else:
                            logger.warning(f"Failed to calculate indicator: {name}")

                except Exception as e:
                    logger.error(f"Error calculating indicator {name}: {str(e)}")

            # Cache results in memory
            self.results = results

            logger.info(f"Calculated {len(results)} indicators successfully (Cache hits: {cache_hits}, misses: {cache_misses})")
            return results

        except Exception as e:
            logger.error(f"Error calculating indicators: {str(e)}")
            return {}

    def calculate_selective(self, data: pd.DataFrame, indicators_to_calculate: Set[str]) -> Dict[str, IndicatorResult]:
        """
        Calculate only specific indicators (Phase 5.5 impact-based updates).

        Args:
            data: OHLCV DataFrame
            indicators_to_calculate: Set of indicator names to calculate

        Returns:
            Dictionary of indicator results for calculated indicators
        """
        try:
            results = {}

            for name in indicators_to_calculate:
                if name not in self.indicators:
                    logger.warning(f"Indicator '{name}' not found for selective calculation")
                    continue

                config = self.indicators[name]
                if not config.enabled:
                    logger.debug(f"Skipping disabled indicator: {name}")
                    continue

                try:
                    # Get indicator instance
                    indicator = TechnicalIndicators.get_indicator(config.indicator_type)

                    # Calculate indicator
                    result = indicator.calculate(data, **config.parameters)

                    if result.metadata.get('calculation_success', False):
                        results[name] = result
                        # Update cached results
                        self.results[name] = result
                        logger.debug(f"Selectively calculated indicator: {name}")
                    else:
                        logger.warning(f"Failed to selectively calculate indicator: {name}")

                except Exception as e:
                    logger.error(f"Error selectively calculating indicator {name}: {str(e)}")

            logger.info(f"Selectively calculated {len(results)} indicators: {list(results.keys())}")
            return results

        except Exception as e:
            logger.error(f"Error in selective calculation: {str(e)}")
            return {}

    def calculate_with_impact_analysis(self, data: pd.DataFrame, change_type: ChangeType = None,
                                     affected_indicators: Set[str] = None) -> Dict[str, IndicatorResult]:
        """
        Calculate indicators using impact analysis (Phase 5.5).

        Args:
            data: OHLCV DataFrame
            change_type: Type of change that triggered this calculation
            affected_indicators: Indicators affected by the change

        Returns:
            Dictionary of indicator results
        """
        try:
            # If no change type specified, calculate all indicators
            if change_type is None:
                return self.calculate_all(data)

            # Register the change if not already registered
            if change_type and affected_indicators is not None:
                change_event = self.impact_manager.register_change(
                    change_type=change_type,
                    affected_indicators=affected_indicators
                )
            else:
                # Use the most recent change event
                recent_changes = self.impact_manager.get_change_history(limit=1)
                if not recent_changes:
                    logger.warning("No change event found, calculating all indicators")
                    return self.calculate_all(data)
                change_event = recent_changes[0]

            # Determine which indicators need recalculation
            indicators_to_recalc = self.impact_manager.get_indicators_to_recalculate(
                change_event, self.indicators
            )

            # If all indicators need recalculation, use the standard method
            if len(indicators_to_recalc) == len(self.indicators):
                logger.info("Impact analysis determined all indicators need recalculation")
                return self.calculate_all(data)

            # Otherwise, use selective calculation
            logger.info(f"Impact analysis determined {len(indicators_to_recalc)} indicators need recalculation")
            return self.calculate_selective(data, indicators_to_recalc)

        except Exception as e:
            logger.error(f"Error in impact-based calculation: {str(e)}")
            # Fallback to calculating all indicators
            return self.calculate_all(data)

    def calculate_with_dependencies(self, data: pd.DataFrame, indicators_to_calculate: Set[str] = None) -> Dict[str, IndicatorResult]:
        """
        Calculate indicators with dependency-aware ordering (Phase 5.5.3).

        Args:
            data: OHLCV DataFrame
            indicators_to_calculate: Set of indicators to calculate (None for all)

        Returns:
            Dictionary of indicator results
        """
        try:
            if indicators_to_calculate is None:
                indicators_to_calculate = set(self.indicators.keys())

            # Auto-detect dependencies for current indicators
            self.dependency_manager.auto_detect_dependencies(self.indicators)

            # Get optimal calculation order based on dependencies
            calculation_order = self.dependency_manager.get_calculation_order(indicators_to_calculate)

            results = {}

            for name in calculation_order:
                if name not in self.indicators:
                    logger.warning(f"Indicator '{name}' not found in calculation order")
                    continue

                config = self.indicators[name]
                if not config.enabled:
                    logger.debug(f"Skipping disabled indicator: {name}")
                    continue

                try:
                    # Get indicator instance
                    indicator = TechnicalIndicators.get_indicator(config.indicator_type)

                    # Calculate indicator
                    result = indicator.calculate(data, **config.parameters)

                    if result.metadata.get('calculation_success', False):
                        results[name] = result
                        # Update cached results
                        self.results[name] = result
                        logger.debug(f"Calculated indicator with dependencies: {name}")
                    else:
                        logger.warning(f"Failed to calculate indicator with dependencies: {name}")

                except Exception as e:
                    logger.error(f"Error calculating indicator {name} with dependencies: {str(e)}")

            logger.info(f"Calculated {len(results)} indicators with dependency ordering: {calculation_order}")
            return results

        except Exception as e:
            logger.error(f"Error in dependency-aware calculation: {str(e)}")
            # Fallback to standard calculation
            return self.calculate_all(data)

    def calculate_cascade(self, data: pd.DataFrame, changed_indicator: str) -> Dict[str, IndicatorResult]:
        """
        Calculate indicators affected by a change using cascade analysis (Phase 5.5.3).

        Args:
            data: OHLCV DataFrame
            changed_indicator: Indicator that changed and triggered cascade

        Returns:
            Dictionary of indicator results for affected indicators
        """
        try:
            # Get indicators that need recalculation due to cascade
            cascade_indicators = self.dependency_manager.get_cascade_indicators(changed_indicator)

            # Filter to only include existing and enabled indicators
            existing_cascade = {name for name in cascade_indicators
                              if name in self.indicators and self.indicators[name].enabled}

            if not existing_cascade:
                logger.debug(f"No cascade indicators found for {changed_indicator}")
                return {}

            # Calculate with dependency-aware ordering
            results = self.calculate_with_dependencies(data, existing_cascade)

            logger.info(f"Cascade calculation for {changed_indicator}: {len(results)} indicators calculated")
            return results

        except Exception as e:
            logger.error(f"Error in cascade calculation: {str(e)}")
            # Fallback to calculating just the changed indicator
            return self.calculate_selective(data, {changed_indicator})
    
    def get_indicator_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific indicator.
        
        Args:
            name: Indicator name
            
        Returns:
            Indicator information or None if not found
        """
        if name not in self.indicators:
            return None
        
        config = self.indicators[name]
        
        try:
            indicator = TechnicalIndicators.get_indicator(config.indicator_type)
            info = indicator.get_info()
            
            info.update({
                'instance_name': name,
                'display_name': config.display_name,
                'enabled': config.enabled,
                'current_parameters': config.parameters
            })
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting indicator info for {name}: {str(e)}")
            return None
    
    def get_all_indicators_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all indicators."""
        info = {}
        
        for name in self.indicators:
            indicator_info = self.get_indicator_info(name)
            if indicator_info:
                info[name] = indicator_info
        
        return info

    # ===== DEPENDENCY MANAGEMENT METHODS (Phase 5.5.3) =====

    def add_indicator_dependency(self, dependent: str, dependency: str,
                               dep_type: DependencyType = DependencyType.DIRECT) -> bool:
        """
        Add a dependency relationship between indicators.

        Args:
            dependent: Indicator that depends on others
            dependency: Indicator that is depended upon
            dep_type: Type of dependency

        Returns:
            True if added successfully, False otherwise
        """
        try:
            # Validate that both indicators exist
            if dependent not in self.indicators:
                logger.warning(f"Dependent indicator '{dependent}' not found")
                return False

            if dependency not in self.indicators:
                logger.warning(f"Dependency indicator '{dependency}' not found")
                return False

            success = self.dependency_manager.add_indicator_dependency(
                dependent, dependency, dep_type
            )

            if success:
                logger.info(f"Added dependency: {dependent} -> {dependency}")

            return success

        except Exception as e:
            logger.error(f"Error adding indicator dependency: {e}")
            return False

    def remove_indicator_dependency(self, dependent: str, dependency: str) -> bool:
        """Remove a dependency relationship between indicators."""
        try:
            success = self.dependency_manager.remove_indicator_dependency(dependent, dependency)

            if success:
                logger.info(f"Removed dependency: {dependent} -> {dependency}")

            return success

        except Exception as e:
            logger.error(f"Error removing indicator dependency: {e}")
            return False

    def get_indicator_dependencies(self, indicator: str) -> Set[str]:
        """Get dependencies of an indicator."""
        return self.dependency_manager.graph.get_dependencies(indicator)

    def get_indicator_dependents(self, indicator: str) -> Set[str]:
        """Get dependents of an indicator."""
        return self.dependency_manager.graph.get_dependents(indicator)

    def auto_detect_dependencies(self) -> int:
        """Auto-detect dependencies for current indicators."""
        return self.dependency_manager.auto_detect_dependencies(self.indicators)

    def get_dependency_statistics(self) -> Dict[str, Any]:
        """Get dependency management statistics."""
        return self.dependency_manager.get_dependency_statistics()

    def get_calculation_order(self, indicators: Set[str] = None) -> List[str]:
        """
        Get optimal calculation order for indicators based on dependencies.

        Args:
            indicators: Set of indicators to order (None for all)

        Returns:
            List of indicators in optimal calculation order
        """
        if indicators is None:
            indicators = set(self.indicators.keys())

        return self.dependency_manager.get_calculation_order(indicators)

    def export_configuration(self) -> str:
        """
        Export current indicator configuration to JSON.
        
        Returns:
            JSON string with configuration
        """
        try:
            config_data = {}
            
            for name, config in self.indicators.items():
                config_data[name] = {
                    'indicator_type': config.indicator_type,
                    'parameters': config.parameters,
                    'enabled': config.enabled,
                    'display_name': config.display_name
                }
            
            return json.dumps(config_data, indent=2)
            
        except Exception as e:
            logger.error(f"Error exporting configuration: {str(e)}")
            return "{}"
    
    def import_configuration(self, config_json: str) -> bool:
        """
        Import indicator configuration from JSON.
        
        Args:
            config_json: JSON string with configuration
            
        Returns:
            True if imported successfully, False otherwise
        """
        try:
            config_data = json.loads(config_json)
            
            # Clear existing indicators
            self.clear_all()
            
            # Add indicators from configuration
            for name, config in config_data.items():
                success = self.add_indicator(
                    name=name,
                    indicator_type=config['indicator_type'],
                    parameters=config.get('parameters', {}),
                    display_name=config.get('display_name')
                )
                
                if success and not config.get('enabled', True):
                    self.disable_indicator(name)
            
            logger.info(f"Imported configuration with {len(config_data)} indicators")
            return True
            
        except Exception as e:
            logger.error(f"Error importing configuration: {str(e)}")
            return False
    
    def clear_all(self):
        """Clear all indicators and results."""
        self.indicators.clear()
        self.results.clear()
        logger.info("Cleared all indicators")
    
    def get_available_indicators(self) -> Dict[str, str]:
        """Get list of available indicator types."""
        return TechnicalIndicators.get_available_indicators()
    
    def get_indicators_by_category(self) -> Dict[str, List[str]]:
        """Get available indicators grouped by category."""
        return TechnicalIndicators.get_indicators_by_category()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of current indicator setup."""
        enabled_count = sum(1 for config in self.indicators.values() if config.enabled)
        
        categories = {}
        for config in self.indicators.values():
            try:
                indicator = TechnicalIndicators.get_indicator(config.indicator_type)
                category = indicator.category
                categories[category] = categories.get(category, 0) + 1
            except:
                pass
        
        return {
            'total_indicators': len(self.indicators),
            'enabled_indicators': enabled_count,
            'disabled_indicators': len(self.indicators) - enabled_count,
            'categories': categories,
            'max_indicators': self.max_indicators,
            'has_results': len(self.results) > 0
        }
