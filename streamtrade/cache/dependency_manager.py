"""
Indicator Dependency Manager for Phase 5.5.3.
Manages indicator-to-indicator dependencies and cascade calculations.
"""

import threading
from typing import Dict, List, Set, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import json
from collections import defaultdict, deque

from ..config.logging_config import get_logger

logger = get_logger(__name__)


class DependencyType(Enum):
    """Types of indicator dependencies."""
    DIRECT = "direct"           # Direct dependency (e.g., MACD Signal depends on MACD)
    DERIVED = "derived"         # Derived calculation (e.g., MACD Histogram depends on MACD + Signal)
    COMPOSITE = "composite"     # Composite indicator (e.g., Stochastic %K and %D)
    OVERLAY = "overlay"         # Overlay dependency (e.g., Bollinger Bands on SMA)


@dataclass
class DependencyRelation:
    """Represents a dependency relationship between indicators."""
    dependent: str              # Indicator that depends on others
    dependency: str             # Indicator that is depended upon
    dependency_type: DependencyType
    parameters: Dict[str, Any]  # Parameters that affect the dependency
    weight: float = 1.0         # Weight of dependency (for optimization)
    
    def __post_init__(self):
        """Validate dependency relation."""
        if self.dependent == self.dependency:
            raise ValueError("Indicator cannot depend on itself")


class DependencyGraph:
    """
    Manages indicator dependency graph and provides topological sorting.
    """
    
    def __init__(self):
        """Initialize Dependency Graph."""
        self._dependencies: Dict[str, Set[str]] = defaultdict(set)  # indicator -> set of dependencies
        self._dependents: Dict[str, Set[str]] = defaultdict(set)    # indicator -> set of dependents
        self._relations: List[DependencyRelation] = []
        self._lock = threading.RLock()
        
        logger.debug("Dependency Graph initialized")
    
    def add_dependency(self, relation: DependencyRelation):
        """
        Add a dependency relationship.
        
        Args:
            relation: DependencyRelation object
        """
        try:
            with self._lock:
                # Check for circular dependencies
                if self._would_create_cycle(relation.dependent, relation.dependency):
                    raise ValueError(f"Adding dependency would create circular dependency: "
                                   f"{relation.dependent} -> {relation.dependency}")
                
                # Add to graph
                self._dependencies[relation.dependent].add(relation.dependency)
                self._dependents[relation.dependency].add(relation.dependent)
                self._relations.append(relation)
                
                logger.debug(f"Added dependency: {relation.dependent} depends on {relation.dependency}")
                
        except Exception as e:
            logger.error(f"Error adding dependency: {e}")
            raise
    
    def remove_dependency(self, dependent: str, dependency: str):
        """
        Remove a dependency relationship.
        
        Args:
            dependent: Dependent indicator name
            dependency: Dependency indicator name
        """
        try:
            with self._lock:
                # Remove from graph
                self._dependencies[dependent].discard(dependency)
                self._dependents[dependency].discard(dependent)
                
                # Remove from relations
                self._relations = [r for r in self._relations 
                                 if not (r.dependent == dependent and r.dependency == dependency)]
                
                logger.debug(f"Removed dependency: {dependent} no longer depends on {dependency}")
                
        except Exception as e:
            logger.error(f"Error removing dependency: {e}")
    
    def get_dependencies(self, indicator: str) -> Set[str]:
        """Get direct dependencies of an indicator."""
        with self._lock:
            return self._dependencies[indicator].copy()
    
    def get_dependents(self, indicator: str) -> Set[str]:
        """Get direct dependents of an indicator."""
        with self._lock:
            return self._dependents[indicator].copy()
    
    def get_all_dependencies(self, indicator: str) -> Set[str]:
        """Get all dependencies (direct and indirect) of an indicator."""
        try:
            with self._lock:
                visited = set()
                to_visit = deque([indicator])
                
                while to_visit:
                    current = to_visit.popleft()
                    if current in visited:
                        continue
                    
                    visited.add(current)
                    dependencies = self._dependencies[current]
                    to_visit.extend(dependencies)
                
                # Remove the indicator itself
                visited.discard(indicator)
                return visited
                
        except Exception as e:
            logger.error(f"Error getting all dependencies for {indicator}: {e}")
            return set()
    
    def get_all_dependents(self, indicator: str) -> Set[str]:
        """Get all dependents (direct and indirect) of an indicator."""
        try:
            with self._lock:
                visited = set()
                to_visit = deque([indicator])
                
                while to_visit:
                    current = to_visit.popleft()
                    if current in visited:
                        continue
                    
                    visited.add(current)
                    dependents = self._dependents[current]
                    to_visit.extend(dependents)
                
                # Remove the indicator itself
                visited.discard(indicator)
                return visited
                
        except Exception as e:
            logger.error(f"Error getting all dependents for {indicator}: {e}")
            return set()
    
    def get_calculation_order(self, indicators: Set[str]) -> List[str]:
        """
        Get optimal calculation order for a set of indicators using topological sort.
        
        Args:
            indicators: Set of indicator names to order
            
        Returns:
            List of indicators in calculation order (dependencies first)
        """
        try:
            with self._lock:
                # Filter graph to only include requested indicators
                filtered_deps = {}
                for indicator in indicators:
                    deps = self._dependencies[indicator].intersection(indicators)
                    if deps:
                        filtered_deps[indicator] = deps
                
                # Topological sort using Kahn's algorithm
                in_degree = defaultdict(int)
                for indicator in indicators:
                    in_degree[indicator] = 0
                
                for indicator, deps in filtered_deps.items():
                    in_degree[indicator] = len(deps)
                
                # Start with indicators that have no dependencies
                queue = deque([ind for ind in indicators if in_degree[ind] == 0])
                result = []
                
                while queue:
                    current = queue.popleft()
                    result.append(current)
                    
                    # Update in-degrees of dependents
                    for dependent in self._dependents[current]:
                        if dependent in indicators:
                            in_degree[dependent] -= 1
                            if in_degree[dependent] == 0:
                                queue.append(dependent)
                
                # Check for circular dependencies
                if len(result) != len(indicators):
                    remaining = indicators - set(result)
                    logger.warning(f"Circular dependencies detected among: {remaining}")
                    # Add remaining indicators in arbitrary order
                    result.extend(remaining)
                
                logger.debug(f"Calculation order for {len(indicators)} indicators: {result}")
                return result
                
        except Exception as e:
            logger.error(f"Error getting calculation order: {e}")
            # Fallback to original order
            return list(indicators)
    
    def _would_create_cycle(self, dependent: str, dependency: str) -> bool:
        """Check if adding a dependency would create a cycle."""
        try:
            # If dependency already depends on dependent (directly or indirectly), 
            # adding this would create a cycle
            all_deps_of_dependency = self.get_all_dependencies(dependency)
            return dependent in all_deps_of_dependency
            
        except Exception as e:
            logger.error(f"Error checking for cycle: {e}")
            return True  # Conservative approach - assume cycle to be safe
    
    def get_graph_statistics(self) -> Dict[str, Any]:
        """Get dependency graph statistics."""
        try:
            with self._lock:
                total_indicators = len(set(self._dependencies.keys()) | set(self._dependents.keys()))
                total_relations = len(self._relations)
                
                # Count by dependency type
                type_counts = defaultdict(int)
                for relation in self._relations:
                    type_counts[relation.dependency_type.value] += 1
                
                # Find indicators with most dependencies/dependents
                max_deps = max((len(deps) for deps in self._dependencies.values()), default=0)
                max_dependents = max((len(deps) for deps in self._dependents.values()), default=0)
                
                return {
                    'total_indicators': total_indicators,
                    'total_relations': total_relations,
                    'dependency_types': dict(type_counts),
                    'max_dependencies': max_deps,
                    'max_dependents': max_dependents,
                    'has_cycles': self._has_cycles()
                }
                
        except Exception as e:
            logger.error(f"Error getting graph statistics: {e}")
            return {'error': str(e)}
    
    def _has_cycles(self) -> bool:
        """Check if the graph has any cycles."""
        try:
            all_indicators = set(self._dependencies.keys()) | set(self._dependents.keys())
            if not all_indicators:
                return False
            
            # Try topological sort - if it fails to include all nodes, there's a cycle
            order = self.get_calculation_order(all_indicators)
            return len(order) != len(all_indicators)
            
        except Exception:
            return True  # Conservative approach


class DependencyManager:
    """
    Manages indicator dependencies and provides cascade calculation support.
    """
    
    def __init__(self):
        """Initialize Dependency Manager."""
        self.graph = DependencyGraph()
        self._predefined_dependencies = {}
        self._lock = threading.RLock()
        
        # Initialize predefined dependencies
        self._initialize_predefined_dependencies()
        
        logger.debug("Dependency Manager initialized")
    
    def _initialize_predefined_dependencies(self):
        """Initialize predefined indicator dependencies."""
        try:
            # MACD dependencies
            self.register_predefined_dependency(
                'MACD_Signal', 'MACD', DependencyType.DERIVED,
                {'signal_period': 9}
            )
            self.register_predefined_dependency(
                'MACD_Histogram', 'MACD', DependencyType.DERIVED,
                {'histogram_calculation': True}
            )
            self.register_predefined_dependency(
                'MACD_Histogram', 'MACD_Signal', DependencyType.DERIVED,
                {'histogram_calculation': True}
            )
            
            # Stochastic dependencies
            self.register_predefined_dependency(
                'Stochastic_D', 'Stochastic_K', DependencyType.DERIVED,
                {'d_period': 3}
            )
            
            # Bollinger Bands dependencies (if implemented as separate indicators)
            self.register_predefined_dependency(
                'BB_Upper', 'SMA', DependencyType.OVERLAY,
                {'period': 20, 'std_dev': 2}
            )
            self.register_predefined_dependency(
                'BB_Lower', 'SMA', DependencyType.OVERLAY,
                {'period': 20, 'std_dev': 2}
            )
            
            logger.info("Predefined dependencies initialized")
            
        except Exception as e:
            logger.error(f"Error initializing predefined dependencies: {e}")
    
    def register_predefined_dependency(self, dependent: str, dependency: str,
                                     dep_type: DependencyType, parameters: Dict[str, Any]):
        """Register a predefined dependency relationship."""
        try:
            self._predefined_dependencies[dependent] = {
                'dependency': dependency,
                'type': dep_type,
                'parameters': parameters
            }
            
        except Exception as e:
            logger.error(f"Error registering predefined dependency: {e}")
    
    def add_indicator_dependency(self, dependent: str, dependency: str,
                               dep_type: DependencyType = DependencyType.DIRECT,
                               parameters: Dict[str, Any] = None) -> bool:
        """
        Add an indicator dependency.
        
        Args:
            dependent: Indicator that depends on others
            dependency: Indicator that is depended upon
            dep_type: Type of dependency
            parameters: Parameters affecting the dependency
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            if parameters is None:
                parameters = {}
            
            relation = DependencyRelation(
                dependent=dependent,
                dependency=dependency,
                dependency_type=dep_type,
                parameters=parameters
            )
            
            self.graph.add_dependency(relation)
            logger.info(f"Added dependency: {dependent} -> {dependency} ({dep_type.value})")
            return True
            
        except Exception as e:
            logger.error(f"Error adding indicator dependency: {e}")
            return False
    
    def remove_indicator_dependency(self, dependent: str, dependency: str) -> bool:
        """Remove an indicator dependency."""
        try:
            self.graph.remove_dependency(dependent, dependency)
            logger.info(f"Removed dependency: {dependent} -> {dependency}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing indicator dependency: {e}")
            return False
    
    def get_calculation_order(self, indicators: Set[str]) -> List[str]:
        """Get optimal calculation order for indicators."""
        return self.graph.get_calculation_order(indicators)
    
    def get_cascade_indicators(self, changed_indicator: str) -> Set[str]:
        """
        Get indicators that need recalculation when an indicator changes.
        
        Args:
            changed_indicator: Indicator that changed
            
        Returns:
            Set of indicators that need recalculation (including the changed one)
        """
        try:
            # Get all dependents (indicators that depend on the changed one)
            cascade_indicators = self.graph.get_all_dependents(changed_indicator)
            
            # Include the changed indicator itself
            cascade_indicators.add(changed_indicator)
            
            logger.debug(f"Cascade for {changed_indicator}: {cascade_indicators}")
            return cascade_indicators
            
        except Exception as e:
            logger.error(f"Error getting cascade indicators: {e}")
            return {changed_indicator}  # Fallback to just the changed indicator
    
    def auto_detect_dependencies(self, indicator_configs: Dict[str, Any]) -> int:
        """
        Auto-detect dependencies based on indicator configurations and predefined rules.
        
        Args:
            indicator_configs: Dictionary of indicator configurations
            
        Returns:
            Number of dependencies detected and added
        """
        try:
            detected_count = 0
            
            for indicator_name, config in indicator_configs.items():
                # Check predefined dependencies
                if indicator_name in self._predefined_dependencies:
                    pred_dep = self._predefined_dependencies[indicator_name]
                    dependency_name = pred_dep['dependency']
                    
                    # Check if dependency exists in current indicators
                    if dependency_name in indicator_configs:
                        success = self.add_indicator_dependency(
                            indicator_name,
                            dependency_name,
                            pred_dep['type'],
                            pred_dep['parameters']
                        )
                        if success:
                            detected_count += 1
            
            logger.info(f"Auto-detected {detected_count} dependencies")
            return detected_count
            
        except Exception as e:
            logger.error(f"Error auto-detecting dependencies: {e}")
            return 0
    
    def get_dependency_statistics(self) -> Dict[str, Any]:
        """Get dependency management statistics."""
        return self.graph.get_graph_statistics()


# Global instance
_dependency_manager = None
_dependency_manager_lock = threading.Lock()


def get_dependency_manager() -> DependencyManager:
    """Get global Dependency Manager instance."""
    global _dependency_manager
    
    if _dependency_manager is None:
        with _dependency_manager_lock:
            if _dependency_manager is None:
                _dependency_manager = DependencyManager()
    
    return _dependency_manager
