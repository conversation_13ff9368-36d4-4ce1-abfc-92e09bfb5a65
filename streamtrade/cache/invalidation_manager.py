"""
Smart Cache Invalidation Manager for Phase 5.5.2.
Provides intelligent cache invalidation based on change analysis.
"""

import threading
from typing import Dict, List, Set, Any, Optional
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import json

from .impact_manager import ChangeType, ChangeEvent, get_impact_manager
from ..config.logging_config import get_logger

logger = get_logger(__name__)


class InvalidationScope(Enum):
    """Scope of cache invalidation."""
    NONE = "none"                    # No invalidation needed
    SINGLE_INDICATOR = "single"      # Single indicator cache
    MULTIPLE_INDICATORS = "multiple" # Multiple specific indicators
    ALL_INDICATORS = "all"           # All indicator caches
    DATA_AND_INDICATORS = "data_all" # Data cache and all indicators
    STYLE_ONLY = "style"             # Only style cache


@dataclass
class InvalidationPlan:
    """Plan for cache invalidation operations."""
    scope: InvalidationScope
    affected_indicators: Set[str]
    affected_data_keys: Set[str]
    affected_style_keys: Set[str]
    reason: str
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        """Ensure all affected sets are sets."""
        if isinstance(self.affected_indicators, (list, tuple)):
            self.affected_indicators = set(self.affected_indicators)
        if isinstance(self.affected_data_keys, (list, tuple)):
            self.affected_data_keys = set(self.affected_data_keys)
        if isinstance(self.affected_style_keys, (list, tuple)):
            self.affected_style_keys = set(self.affected_style_keys)


class InvalidationAnalyzer:
    """
    Analyzes changes and determines optimal cache invalidation strategy.
    """
    
    def __init__(self):
        """Initialize Invalidation Analyzer."""
        self._lock = threading.RLock()
        logger.debug("Invalidation Analyzer initialized")
    
    def analyze_invalidation_needs(self, change_event: ChangeEvent,
                                 current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """
        Analyze change event and determine cache invalidation plan.
        
        Args:
            change_event: The change event to analyze
            current_cache_state: Current state of cache system
            
        Returns:
            InvalidationPlan with specific invalidation strategy
        """
        try:
            with self._lock:
                if change_event.change_type == ChangeType.DATA_RELOAD:
                    return self._plan_data_reload_invalidation(change_event, current_cache_state)
                
                elif change_event.change_type == ChangeType.DATA_EXTEND:
                    return self._plan_data_extend_invalidation(change_event, current_cache_state)
                
                elif change_event.change_type == ChangeType.TIMEFRAME_SWITCH:
                    return self._plan_timeframe_switch_invalidation(change_event, current_cache_state)
                
                elif change_event.change_type == ChangeType.INDICATOR_PARAM:
                    return self._plan_indicator_param_invalidation(change_event, current_cache_state)
                
                elif change_event.change_type == ChangeType.INDICATOR_ADD:
                    return self._plan_indicator_add_invalidation(change_event, current_cache_state)
                
                elif change_event.change_type == ChangeType.INDICATOR_REMOVE:
                    return self._plan_indicator_remove_invalidation(change_event, current_cache_state)
                
                elif change_event.change_type == ChangeType.INDICATOR_TOGGLE:
                    return self._plan_indicator_toggle_invalidation(change_event, current_cache_state)
                
                else:
                    logger.warning(f"Unknown change type for invalidation: {change_event.change_type}")
                    return self._plan_safe_fallback_invalidation(change_event, current_cache_state)
                    
        except Exception as e:
            logger.error(f"Error analyzing invalidation needs: {e}")
            return self._plan_safe_fallback_invalidation(change_event, current_cache_state)
    
    def _plan_data_reload_invalidation(self, change_event: ChangeEvent,
                                     current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """Plan invalidation for data reload."""
        return InvalidationPlan(
            scope=InvalidationScope.DATA_AND_INDICATORS,
            affected_indicators=set(current_cache_state.get('indicators', {}).keys()),
            affected_data_keys=set(current_cache_state.get('data_keys', [])),
            affected_style_keys=set(),  # Styles can be preserved
            reason="Data reload requires full cache invalidation",
            metadata=change_event.metadata
        )
    
    def _plan_data_extend_invalidation(self, change_event: ChangeEvent,
                                     current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """Plan invalidation for data extension."""
        return InvalidationPlan(
            scope=InvalidationScope.ALL_INDICATORS,
            affected_indicators=set(current_cache_state.get('indicators', {}).keys()),
            affected_data_keys=set(),  # Data cache can be extended, not invalidated
            affected_style_keys=set(),  # Styles preserved
            reason="Data extension requires indicator cache update",
            metadata=change_event.metadata
        )
    
    def _plan_timeframe_switch_invalidation(self, change_event: ChangeEvent,
                                          current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """Plan invalidation for timeframe switch."""
        # Check if we have cached data for the new timeframe
        new_timeframe = change_event.metadata.get('new_timeframe')
        if new_timeframe and self._has_cached_timeframe_data(new_timeframe, current_cache_state):
            # We have cached data, only need to invalidate indicators for new timeframe
            return InvalidationPlan(
                scope=InvalidationScope.ALL_INDICATORS,
                affected_indicators=set(current_cache_state.get('indicators', {}).keys()),
                affected_data_keys=set(),
                affected_style_keys=set(),
                reason=f"Timeframe switch to {new_timeframe} with cached data",
                metadata=change_event.metadata
            )
        else:
            # No cached data, need full invalidation
            return InvalidationPlan(
                scope=InvalidationScope.DATA_AND_INDICATORS,
                affected_indicators=set(current_cache_state.get('indicators', {}).keys()),
                affected_data_keys=set(current_cache_state.get('data_keys', [])),
                affected_style_keys=set(),
                reason=f"Timeframe switch to {new_timeframe} without cached data",
                metadata=change_event.metadata
            )
    
    def _plan_indicator_param_invalidation(self, change_event: ChangeEvent,
                                         current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """Plan invalidation for indicator parameter change."""
        return InvalidationPlan(
            scope=InvalidationScope.SINGLE_INDICATOR,
            affected_indicators=change_event.affected_indicators,
            affected_data_keys=set(),  # Data cache preserved
            affected_style_keys=change_event.affected_indicators,  # Style cache for this indicator
            reason="Parameter change affects only specific indicator",
            metadata=change_event.metadata
        )
    
    def _plan_indicator_add_invalidation(self, change_event: ChangeEvent,
                                       current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """Plan invalidation for indicator addition."""
        return InvalidationPlan(
            scope=InvalidationScope.NONE,  # No existing cache to invalidate
            affected_indicators=set(),
            affected_data_keys=set(),
            affected_style_keys=set(),
            reason="Adding indicator doesn't invalidate existing cache",
            metadata=change_event.metadata
        )
    
    def _plan_indicator_remove_invalidation(self, change_event: ChangeEvent,
                                          current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """Plan invalidation for indicator removal."""
        return InvalidationPlan(
            scope=InvalidationScope.SINGLE_INDICATOR,
            affected_indicators=change_event.affected_indicators,
            affected_data_keys=set(),
            affected_style_keys=change_event.affected_indicators,
            reason="Remove indicator cache and style",
            metadata=change_event.metadata
        )
    
    def _plan_indicator_toggle_invalidation(self, change_event: ChangeEvent,
                                          current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """Plan invalidation for indicator toggle."""
        enabled = change_event.metadata.get('enabled', False)
        if enabled:
            # Enabling indicator - no cache to invalidate, will calculate fresh
            return InvalidationPlan(
                scope=InvalidationScope.NONE,
                affected_indicators=set(),
                affected_data_keys=set(),
                affected_style_keys=set(),
                reason="Enabling indicator - no cache invalidation needed",
                metadata=change_event.metadata
            )
        else:
            # Disabling indicator - can keep cache for potential re-enable
            return InvalidationPlan(
                scope=InvalidationScope.NONE,
                affected_indicators=set(),
                affected_data_keys=set(),
                affected_style_keys=set(),
                reason="Disabling indicator - preserving cache for potential re-enable",
                metadata=change_event.metadata
            )
    
    def _plan_safe_fallback_invalidation(self, change_event: ChangeEvent,
                                       current_cache_state: Dict[str, Any]) -> InvalidationPlan:
        """Plan safe fallback invalidation for unknown changes."""
        return InvalidationPlan(
            scope=InvalidationScope.DATA_AND_INDICATORS,
            affected_indicators=set(current_cache_state.get('indicators', {}).keys()),
            affected_data_keys=set(current_cache_state.get('data_keys', [])),
            affected_style_keys=set(current_cache_state.get('style_keys', [])),
            reason="Unknown change type - safe fallback to full invalidation",
            metadata=change_event.metadata
        )
    
    def _has_cached_timeframe_data(self, timeframe: str, current_cache_state: Dict[str, Any]) -> bool:
        """Check if we have cached data for the specified timeframe."""
        cached_timeframes = current_cache_state.get('cached_timeframes', set())
        return timeframe in cached_timeframes


class SmartInvalidationManager:
    """
    Smart Cache Invalidation Manager.
    Coordinates intelligent cache invalidation based on change analysis.
    """
    
    def __init__(self, disk_cache=None, indicator_cache=None):
        """
        Initialize Smart Invalidation Manager.
        
        Args:
            disk_cache: SmartDiskCache instance
            indicator_cache: IndicatorCache instance
        """
        self.disk_cache = disk_cache
        self.indicator_cache = indicator_cache
        self.analyzer = InvalidationAnalyzer()
        self.impact_manager = get_impact_manager()
        
        self._invalidation_history: List[InvalidationPlan] = []
        self._lock = threading.RLock()
        
        logger.debug("Smart Invalidation Manager initialized")
    
    def invalidate_based_on_change(self, change_event: ChangeEvent) -> InvalidationPlan:
        """
        Perform smart cache invalidation based on change event.
        
        Args:
            change_event: The change event that triggered invalidation
            
        Returns:
            InvalidationPlan that was executed
        """
        try:
            with self._lock:
                # Get current cache state
                current_cache_state = self._get_current_cache_state()
                
                # Analyze invalidation needs
                invalidation_plan = self.analyzer.analyze_invalidation_needs(
                    change_event, current_cache_state
                )
                
                # Execute invalidation plan
                self._execute_invalidation_plan(invalidation_plan)
                
                # Record in history
                self._invalidation_history.append(invalidation_plan)
                if len(self._invalidation_history) > 50:
                    self._invalidation_history.pop(0)
                
                logger.info(f"Smart invalidation completed: {invalidation_plan.scope.value} "
                           f"affecting {len(invalidation_plan.affected_indicators)} indicators")
                
                return invalidation_plan
                
        except Exception as e:
            logger.error(f"Error in smart invalidation: {e}")
            # Fallback to safe invalidation
            return self._execute_safe_fallback_invalidation(change_event)
    
    def _get_current_cache_state(self) -> Dict[str, Any]:
        """Get current state of cache system."""
        cache_state = {
            'indicators': {},
            'data_keys': [],
            'style_keys': [],
            'cached_timeframes': set()
        }
        
        try:
            # Get indicator cache state
            if self.indicator_cache:
                indicator_stats = self.indicator_cache.get_indicator_stats()
                cache_state['indicators'] = indicator_stats.get('by_indicator', {})
            
            # Get disk cache state
            if self.disk_cache:
                disk_stats = self.disk_cache.get_cache_stats()
                cache_state['data_keys'] = list(disk_stats.get('data_entries', {}).keys())
                cache_state['cached_timeframes'] = set(disk_stats.get('timeframes', []))
            
        except Exception as e:
            logger.warning(f"Error getting cache state: {e}")
        
        return cache_state
    
    def _execute_invalidation_plan(self, plan: InvalidationPlan):
        """Execute the invalidation plan."""
        try:
            if plan.scope == InvalidationScope.NONE:
                logger.debug("No cache invalidation needed")
                return
            
            elif plan.scope == InvalidationScope.SINGLE_INDICATOR:
                self._invalidate_single_indicator(plan.affected_indicators)
            
            elif plan.scope == InvalidationScope.MULTIPLE_INDICATORS:
                self._invalidate_multiple_indicators(plan.affected_indicators)
            
            elif plan.scope == InvalidationScope.ALL_INDICATORS:
                self._invalidate_all_indicators()
            
            elif plan.scope == InvalidationScope.DATA_AND_INDICATORS:
                self._invalidate_data_and_indicators(plan.affected_data_keys)
            
            elif plan.scope == InvalidationScope.STYLE_ONLY:
                self._invalidate_styles(plan.affected_style_keys)
            
            # Always invalidate affected styles
            if plan.affected_style_keys:
                self._invalidate_styles(plan.affected_style_keys)
                
        except Exception as e:
            logger.error(f"Error executing invalidation plan: {e}")
    
    def _invalidate_single_indicator(self, indicators: Set[str]):
        """Invalidate cache for single indicator."""
        if self.indicator_cache:
            for indicator_name in indicators:
                removed_count = self.indicator_cache.invalidate_indicator(indicator_name, "current_data")
                logger.debug(f"Invalidated {removed_count} cache entries for indicator: {indicator_name}")
    
    def _invalidate_multiple_indicators(self, indicators: Set[str]):
        """Invalidate cache for multiple indicators."""
        for indicator_name in indicators:
            self._invalidate_single_indicator({indicator_name})
    
    def _invalidate_all_indicators(self):
        """Invalidate all indicator caches."""
        if self.indicator_cache:
            cleanup_results = self.indicator_cache.cleanup_indicator_cache()
            logger.debug(f"Invalidated all indicator caches: {cleanup_results}")
    
    def _invalidate_data_and_indicators(self, data_keys: Set[str]):
        """Invalidate data cache and all dependent indicators."""
        # Invalidate data cache
        if self.disk_cache and data_keys:
            for data_key in data_keys:
                self.disk_cache.remove_entry(data_key)
                logger.debug(f"Invalidated data cache: {data_key}")
        
        # Invalidate all indicators
        self._invalidate_all_indicators()
    
    def _invalidate_styles(self, style_keys: Set[str]):
        """Invalidate style caches."""
        if self.indicator_cache:
            for style_key in style_keys:
                # Style invalidation logic would go here
                logger.debug(f"Invalidated style cache: {style_key}")
    
    def _execute_safe_fallback_invalidation(self, change_event: ChangeEvent) -> InvalidationPlan:
        """Execute safe fallback invalidation."""
        plan = InvalidationPlan(
            scope=InvalidationScope.DATA_AND_INDICATORS,
            affected_indicators=set(),
            affected_data_keys=set(),
            affected_style_keys=set(),
            reason="Safe fallback due to error",
            metadata=change_event.metadata
        )
        
        self._execute_invalidation_plan(plan)
        return plan
    
    def get_invalidation_statistics(self) -> Dict[str, Any]:
        """Get invalidation statistics."""
        try:
            with self._lock:
                if not self._invalidation_history:
                    return {
                        'total_invalidations': 0,
                        'scope_counts': {},
                        'recent_invalidations': []
                    }
                
                # Count scopes
                scope_counts = {}
                for plan in self._invalidation_history:
                    scope = plan.scope.value
                    scope_counts[scope] = scope_counts.get(scope, 0) + 1
                
                # Recent invalidations
                recent = []
                for plan in self._invalidation_history[-5:]:
                    recent.append({
                        'scope': plan.scope.value,
                        'affected_indicators': len(plan.affected_indicators),
                        'reason': plan.reason
                    })
                
                return {
                    'total_invalidations': len(self._invalidation_history),
                    'scope_counts': scope_counts,
                    'recent_invalidations': recent
                }
                
        except Exception as e:
            logger.error(f"Error getting invalidation statistics: {e}")
            return {'error': str(e)}


# Global instance
_invalidation_manager = None
_invalidation_manager_lock = threading.Lock()


def get_invalidation_manager(disk_cache=None, indicator_cache=None) -> SmartInvalidationManager:
    """Get global Smart Invalidation Manager instance."""
    global _invalidation_manager
    
    if _invalidation_manager is None:
        with _invalidation_manager_lock:
            if _invalidation_manager is None:
                _invalidation_manager = SmartInvalidationManager(disk_cache, indicator_cache)
    
    return _invalidation_manager
