"""
Impact Manager for Advanced Indicator Cache Strategy.
Manages impact-based updates to optimize indicator recalculation.
"""

import threading
from typing import Dict, List, Set, Any, Optional
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import json

from ..config.logging_config import get_logger

logger = get_logger(__name__)


class ChangeType(Enum):
    """Types of changes that can trigger indicator updates."""
    DATA_RELOAD = "data_reload"           # Complete data reload
    DATA_EXTEND = "data_extend"           # New data added
    TIMEFRAME_SWITCH = "timeframe_switch" # Timeframe changed
    INDICATOR_PARAM = "indicator_param"   # Single indicator parameter changed
    INDICATOR_ADD = "indicator_add"       # New indicator added
    INDICATOR_REMOVE = "indicator_remove" # Indicator removed
    INDICATOR_TOGGLE = "indicator_toggle" # Indicator enabled/disabled


@dataclass
class ChangeEvent:
    """Represents a change event that may trigger indicator updates."""
    change_type: ChangeType
    timestamp: datetime
    affected_indicators: Set[str]
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        """Ensure affected_indicators is a set."""
        if isinstance(self.affected_indicators, (list, tuple)):
            self.affected_indicators = set(self.affected_indicators)


class ImpactAnalyzer:
    """
    Analyzes the impact of changes and determines which indicators need recalculation.
    """
    
    def __init__(self):
        """Initialize Impact Analyzer."""
        self._lock = threading.RLock()
        logger.debug("Impact Analyzer initialized")
    
    def analyze_change_impact(self, change_event: ChangeEvent, 
                            current_indicators: Dict[str, Any]) -> Set[str]:
        """
        Analyze the impact of a change and return indicators that need recalculation.
        
        Args:
            change_event: The change event to analyze
            current_indicators: Currently active indicators
            
        Returns:
            Set of indicator names that need recalculation
        """
        try:
            with self._lock:
                if change_event.change_type == ChangeType.DATA_RELOAD:
                    # Data reload affects all indicators
                    return set(current_indicators.keys())
                
                elif change_event.change_type == ChangeType.DATA_EXTEND:
                    # New data affects all indicators (need to extend calculations)
                    return set(current_indicators.keys())
                
                elif change_event.change_type == ChangeType.TIMEFRAME_SWITCH:
                    # Timeframe switch affects all indicators (different data)
                    return set(current_indicators.keys())
                
                elif change_event.change_type == ChangeType.INDICATOR_PARAM:
                    # Parameter change affects only the specific indicator
                    return change_event.affected_indicators
                
                elif change_event.change_type == ChangeType.INDICATOR_ADD:
                    # Adding indicator affects only the new indicator
                    return change_event.affected_indicators
                
                elif change_event.change_type == ChangeType.INDICATOR_REMOVE:
                    # Removing indicator affects no other indicators
                    return set()
                
                elif change_event.change_type == ChangeType.INDICATOR_TOGGLE:
                    # Toggling indicator affects only that indicator (if being enabled)
                    # If being disabled, no recalculation needed
                    enabled = change_event.metadata.get('enabled', False)
                    if enabled:
                        return change_event.affected_indicators
                    else:
                        return set()
                
                else:
                    logger.warning(f"Unknown change type: {change_event.change_type}")
                    # Default to recalculating all indicators for safety
                    return set(current_indicators.keys())
                    
        except Exception as e:
            logger.error(f"Error analyzing change impact: {e}")
            # Default to recalculating all indicators for safety
            return set(current_indicators.keys())


class ImpactManager:
    """
    Manages impact-based updates for indicator calculations.
    Coordinates between change detection and selective recalculation.
    """
    
    def __init__(self):
        """Initialize Impact Manager."""
        self.analyzer = ImpactAnalyzer()
        self._change_history: List[ChangeEvent] = []
        self._lock = threading.RLock()
        
        logger.debug("Impact Manager initialized")
    
    def register_change(self, change_type: ChangeType, affected_indicators: Set[str] = None,
                       metadata: Dict[str, Any] = None) -> ChangeEvent:
        """
        Register a change event.
        
        Args:
            change_type: Type of change
            affected_indicators: Indicators affected by the change
            metadata: Additional metadata about the change
            
        Returns:
            ChangeEvent object
        """
        try:
            with self._lock:
                if affected_indicators is None:
                    affected_indicators = set()
                if metadata is None:
                    metadata = {}
                
                change_event = ChangeEvent(
                    change_type=change_type,
                    timestamp=datetime.now(),
                    affected_indicators=affected_indicators,
                    metadata=metadata
                )
                
                # Add to history (keep last 100 events)
                self._change_history.append(change_event)
                if len(self._change_history) > 100:
                    self._change_history.pop(0)
                
                logger.debug(f"Registered change: {change_type.value} affecting {len(affected_indicators)} indicators")
                return change_event
                
        except Exception as e:
            logger.error(f"Error registering change: {e}")
            raise
    
    def get_indicators_to_recalculate(self, change_event: ChangeEvent,
                                    current_indicators: Dict[str, Any]) -> Set[str]:
        """
        Get the set of indicators that need recalculation based on a change event.
        
        Args:
            change_event: The change event
            current_indicators: Currently active indicators
            
        Returns:
            Set of indicator names to recalculate
        """
        try:
            indicators_to_recalc = self.analyzer.analyze_change_impact(
                change_event, current_indicators
            )
            
            # Filter to only include currently active indicators
            active_indicators = set(current_indicators.keys())
            indicators_to_recalc = indicators_to_recalc.intersection(active_indicators)
            
            logger.info(f"Change {change_event.change_type.value} requires recalculation of "
                       f"{len(indicators_to_recalc)} indicators: {list(indicators_to_recalc)}")
            
            return indicators_to_recalc
            
        except Exception as e:
            logger.error(f"Error determining indicators to recalculate: {e}")
            # Default to all indicators for safety
            return set(current_indicators.keys())
    
    def get_change_history(self, limit: int = 10) -> List[ChangeEvent]:
        """
        Get recent change history.
        
        Args:
            limit: Maximum number of events to return
            
        Returns:
            List of recent change events
        """
        with self._lock:
            return self._change_history[-limit:] if self._change_history else []
    
    def get_impact_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about impact analysis.
        
        Returns:
            Dictionary with impact statistics
        """
        try:
            with self._lock:
                if not self._change_history:
                    return {
                        'total_changes': 0,
                        'change_types': {},
                        'recent_changes': []
                    }
                
                # Count change types
                change_type_counts = {}
                for event in self._change_history:
                    change_type = event.change_type.value
                    change_type_counts[change_type] = change_type_counts.get(change_type, 0) + 1
                
                # Recent changes (last 5)
                recent_changes = []
                for event in self._change_history[-5:]:
                    recent_changes.append({
                        'type': event.change_type.value,
                        'timestamp': event.timestamp.isoformat(),
                        'affected_count': len(event.affected_indicators)
                    })
                
                return {
                    'total_changes': len(self._change_history),
                    'change_types': change_type_counts,
                    'recent_changes': recent_changes
                }
                
        except Exception as e:
            logger.error(f"Error getting impact statistics: {e}")
            return {'error': str(e)}
    
    def clear_history(self):
        """Clear change history."""
        with self._lock:
            self._change_history.clear()
            logger.debug("Impact Manager history cleared")


# Global instance
_impact_manager = None
_impact_manager_lock = threading.Lock()


def get_impact_manager() -> ImpactManager:
    """Get global Impact Manager instance."""
    global _impact_manager
    
    if _impact_manager is None:
        with _impact_manager_lock:
            if _impact_manager is None:
                _impact_manager = ImpactManager()
    
    return _impact_manager
