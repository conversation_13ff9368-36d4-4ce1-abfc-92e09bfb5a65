"""
Cache Coherency Manager for Phase 5.5.4.
Provides centralized coordination of all cache systems for optimal performance.
"""

import threading
from typing import Dict, List, Set, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import time

from .impact_manager import get_impact_manager, ChangeType, ChangeEvent
from .invalidation_manager import get_invalidation_manager
from .dependency_manager import get_dependency_manager
from ..config.logging_config import get_logger

logger = get_logger(__name__)


class CoherencyState(Enum):
    """Cache coherency states."""
    COHERENT = "coherent"           # All caches are consistent
    UPDATING = "updating"           # Cache update in progress
    INCONSISTENT = "inconsistent"   # Caches may be inconsistent
    ERROR = "error"                 # Error state requiring recovery


@dataclass
class CoherencyOperation:
    """Represents a cache coherency operation."""
    operation_id: str
    operation_type: str
    start_time: datetime
    end_time: Optional[datetime]
    state: CoherencyState
    affected_systems: Set[str]
    metadata: Dict[str, Any]
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """Ensure affected_systems is a set."""
        if isinstance(self.affected_systems, (list, tuple)):
            self.affected_systems = set(self.affected_systems)


class PerformanceMonitor:
    """
    Monitors cache system performance and provides optimization recommendations.
    """
    
    def __init__(self):
        """Initialize Performance Monitor."""
        self._metrics: Dict[str, List[float]] = {}
        self._operation_times: Dict[str, List[float]] = {}
        self._cache_hit_rates: Dict[str, List[float]] = {}
        self._lock = threading.RLock()
        
        logger.debug("Performance Monitor initialized")
    
    def record_operation_time(self, operation: str, duration: float):
        """Record operation execution time."""
        try:
            with self._lock:
                if operation not in self._operation_times:
                    self._operation_times[operation] = []
                
                self._operation_times[operation].append(duration)
                
                # Keep only last 100 measurements
                if len(self._operation_times[operation]) > 100:
                    self._operation_times[operation].pop(0)
                    
        except Exception as e:
            logger.error(f"Error recording operation time: {e}")
    
    def record_cache_hit_rate(self, cache_system: str, hit_rate: float):
        """Record cache hit rate."""
        try:
            with self._lock:
                if cache_system not in self._cache_hit_rates:
                    self._cache_hit_rates[cache_system] = []
                
                self._cache_hit_rates[cache_system].append(hit_rate)
                
                # Keep only last 50 measurements
                if len(self._cache_hit_rates[cache_system]) > 50:
                    self._cache_hit_rates[cache_system].pop(0)
                    
        except Exception as e:
            logger.error(f"Error recording cache hit rate: {e}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary and recommendations."""
        try:
            with self._lock:
                summary = {
                    'operation_performance': {},
                    'cache_performance': {},
                    'recommendations': []
                }
                
                # Analyze operation times
                for operation, times in self._operation_times.items():
                    if times:
                        avg_time = sum(times) / len(times)
                        max_time = max(times)
                        min_time = min(times)
                        
                        summary['operation_performance'][operation] = {
                            'average_ms': round(avg_time * 1000, 2),
                            'max_ms': round(max_time * 1000, 2),
                            'min_ms': round(min_time * 1000, 2),
                            'sample_count': len(times)
                        }
                        
                        # Generate recommendations
                        if avg_time > 0.5:  # > 500ms
                            summary['recommendations'].append(
                                f"Operation '{operation}' is slow (avg: {avg_time*1000:.1f}ms). Consider optimization."
                            )
                
                # Analyze cache hit rates
                for cache_system, rates in self._cache_hit_rates.items():
                    if rates:
                        avg_rate = sum(rates) / len(rates)
                        latest_rate = rates[-1]
                        
                        summary['cache_performance'][cache_system] = {
                            'average_hit_rate': round(avg_rate * 100, 1),
                            'latest_hit_rate': round(latest_rate * 100, 1),
                            'sample_count': len(rates)
                        }
                        
                        # Generate recommendations
                        if avg_rate < 0.7:  # < 70%
                            summary['recommendations'].append(
                                f"Cache '{cache_system}' has low hit rate ({avg_rate*100:.1f}%). Consider cache tuning."
                            )
                
                return summary
                
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {'error': str(e)}


class CacheCoherencyManager:
    """
    Centralized Cache Coherency Manager.
    Coordinates all cache systems for optimal performance and consistency.
    """
    
    def __init__(self, disk_cache=None, indicator_cache=None):
        """
        Initialize Cache Coherency Manager.
        
        Args:
            disk_cache: SmartDiskCache instance
            indicator_cache: IndicatorCache instance
        """
        self.disk_cache = disk_cache
        self.indicator_cache = indicator_cache
        
        # Initialize sub-managers
        self.impact_manager = get_impact_manager()
        self.invalidation_manager = get_invalidation_manager(disk_cache, indicator_cache)
        self.dependency_manager = get_dependency_manager()
        
        # Initialize performance monitor
        self.performance_monitor = PerformanceMonitor()
        
        # Operation tracking
        self._operations: Dict[str, CoherencyOperation] = {}
        self._current_state = CoherencyState.COHERENT
        self._operation_counter = 0
        self._lock = threading.RLock()
        
        logger.info("Cache Coherency Manager initialized")
    
    def coordinate_cache_operation(self, operation_type: str, change_event: ChangeEvent,
                                 metadata: Dict[str, Any] = None) -> str:
        """
        Coordinate a cache operation across all systems.
        
        Args:
            operation_type: Type of operation (e.g., 'data_reload', 'parameter_change')
            change_event: Change event that triggered the operation
            metadata: Additional operation metadata
            
        Returns:
            Operation ID for tracking
        """
        start_time = time.time()
        operation_id = None
        
        try:
            with self._lock:
                # Generate operation ID
                self._operation_counter += 1
                operation_id = f"op_{self._operation_counter}_{int(time.time())}"
                
                # Create operation record
                operation = CoherencyOperation(
                    operation_id=operation_id,
                    operation_type=operation_type,
                    start_time=datetime.now(),
                    end_time=None,
                    state=CoherencyState.UPDATING,
                    affected_systems=set(),
                    metadata=metadata or {}
                )
                
                self._operations[operation_id] = operation
                self._current_state = CoherencyState.UPDATING
                
                logger.info(f"Starting coordinated cache operation: {operation_id} ({operation_type})")
                
                # Step 1: Impact Analysis
                current_indicators = metadata.get('current_indicators', {}) if metadata else {}
                indicators_to_recalc = self.impact_manager.get_indicators_to_recalculate(
                    change_event, current_indicators
                )
                operation.affected_systems.add('impact_analysis')
                
                # Step 2: Dependency Analysis
                if indicators_to_recalc:
                    # Get cascade indicators for dependency management
                    all_affected = set()
                    for indicator in indicators_to_recalc:
                        cascade = self.dependency_manager.get_cascade_indicators(indicator)
                        all_affected.update(cascade)
                    
                    # Update indicators to recalculate with cascade
                    indicators_to_recalc = all_affected
                    operation.affected_systems.add('dependency_analysis')
                
                # Step 3: Smart Invalidation
                invalidation_plan = self.invalidation_manager.invalidate_based_on_change(change_event)
                operation.affected_systems.add('cache_invalidation')
                
                # Step 4: Update operation metadata
                operation.metadata.update({
                    'indicators_to_recalculate': list(indicators_to_recalc),
                    'invalidation_scope': invalidation_plan.scope.value,
                    'affected_indicators_count': len(indicators_to_recalc),
                    'invalidated_entries': len(invalidation_plan.affected_indicators)
                })
                
                # Step 5: Mark operation as complete
                operation.end_time = datetime.now()
                operation.state = CoherencyState.COHERENT
                self._current_state = CoherencyState.COHERENT
                
                # Record performance metrics
                duration = time.time() - start_time
                self.performance_monitor.record_operation_time(operation_type, duration)
                
                logger.info(f"Completed coordinated cache operation: {operation_id} "
                           f"({duration*1000:.1f}ms, {len(indicators_to_recalc)} indicators)")
                
                return operation_id
                
        except Exception as e:
            logger.error(f"Error in coordinated cache operation: {e}")
            
            # Mark operation as error
            if operation_id and operation_id in self._operations:
                self._operations[operation_id].state = CoherencyState.ERROR
                self._operations[operation_id].error_message = str(e)
                self._operations[operation_id].end_time = datetime.now()
            
            self._current_state = CoherencyState.ERROR
            
            # Record error performance
            if operation_id:
                duration = time.time() - start_time
                self.performance_monitor.record_operation_time(f"{operation_type}_error", duration)
            
            raise
    
    def get_coherency_status(self) -> Dict[str, Any]:
        """Get current cache coherency status."""
        try:
            with self._lock:
                # Get recent operations (last 10)
                recent_ops = []
                sorted_ops = sorted(
                    self._operations.values(),
                    key=lambda x: x.start_time,
                    reverse=True
                )[:10]
                
                for op in sorted_ops:
                    duration = None
                    if op.end_time:
                        duration = (op.end_time - op.start_time).total_seconds()
                    
                    recent_ops.append({
                        'operation_id': op.operation_id,
                        'type': op.operation_type,
                        'state': op.state.value,
                        'duration_ms': round(duration * 1000, 1) if duration else None,
                        'affected_systems': list(op.affected_systems),
                        'error': op.error_message
                    })
                
                # Get system statistics
                impact_stats = self.impact_manager.get_impact_statistics()
                invalidation_stats = self.invalidation_manager.get_invalidation_statistics()
                dependency_stats = self.dependency_manager.get_dependency_statistics()
                performance_summary = self.performance_monitor.get_performance_summary()
                
                return {
                    'current_state': self._current_state.value,
                    'total_operations': len(self._operations),
                    'recent_operations': recent_ops,
                    'system_statistics': {
                        'impact_manager': impact_stats,
                        'invalidation_manager': invalidation_stats,
                        'dependency_manager': dependency_stats
                    },
                    'performance': performance_summary
                }
                
        except Exception as e:
            logger.error(f"Error getting coherency status: {e}")
            return {'error': str(e)}
    
    def optimize_cache_performance(self) -> Dict[str, Any]:
        """Analyze and optimize cache performance."""
        try:
            optimization_results = {
                'optimizations_applied': [],
                'recommendations': [],
                'performance_before': {},
                'performance_after': {}
            }
            
            # Get current performance
            performance_summary = self.performance_monitor.get_performance_summary()
            optimization_results['performance_before'] = performance_summary
            
            # Analyze and apply optimizations
            
            # 1. Cache hit rate optimization
            cache_perf = performance_summary.get('cache_performance', {})
            for cache_system, metrics in cache_perf.items():
                hit_rate = metrics.get('average_hit_rate', 0)
                if hit_rate < 70:  # < 70%
                    optimization_results['recommendations'].append(
                        f"Consider increasing cache size for {cache_system} (current hit rate: {hit_rate}%)"
                    )
            
            # 2. Operation performance optimization
            op_perf = performance_summary.get('operation_performance', {})
            slow_operations = []
            for operation, metrics in op_perf.items():
                avg_time = metrics.get('average_ms', 0)
                if avg_time > 500:  # > 500ms
                    slow_operations.append((operation, avg_time))
            
            if slow_operations:
                optimization_results['recommendations'].append(
                    f"Slow operations detected: {slow_operations}. Consider optimization."
                )
            
            # 3. Memory usage optimization
            if self.disk_cache:
                try:
                    cache_stats = self.disk_cache.get_cache_stats()
                    cache_size_gb = cache_stats.get('total_size_gb', 0)
                    max_size_gb = cache_stats.get('max_size_gb', 10)
                    
                    if cache_size_gb > max_size_gb * 0.9:  # > 90% full
                        optimization_results['recommendations'].append(
                            f"Cache is {cache_size_gb:.1f}GB / {max_size_gb}GB. Consider cleanup or size increase."
                        )
                        
                        # Trigger cache cleanup
                        cleanup_results = self.disk_cache.cleanup_cache()
                        optimization_results['optimizations_applied'].append(
                            f"Cache cleanup performed: {cleanup_results}"
                        )
                        
                except Exception as e:
                    logger.warning(f"Error checking disk cache stats: {e}")
            
            # 4. Dependency optimization
            dep_stats = self.dependency_manager.get_dependency_statistics()
            if dep_stats.get('has_cycles', False):
                optimization_results['recommendations'].append(
                    "Circular dependencies detected. Review indicator dependencies."
                )
            
            logger.info(f"Cache optimization completed: {len(optimization_results['optimizations_applied'])} applied, "
                       f"{len(optimization_results['recommendations'])} recommendations")
            
            return optimization_results
            
        except Exception as e:
            logger.error(f"Error optimizing cache performance: {e}")
            return {'error': str(e)}
    
    def recover_from_error(self) -> bool:
        """Attempt to recover from error state."""
        try:
            with self._lock:
                if self._current_state != CoherencyState.ERROR:
                    logger.info("System not in error state, no recovery needed")
                    return True
                
                logger.info("Attempting cache coherency recovery...")
                
                # 1. Clear any stuck operations
                stuck_operations = [
                    op for op in self._operations.values()
                    if op.state == CoherencyState.UPDATING and 
                    (datetime.now() - op.start_time).total_seconds() > 300  # 5 minutes
                ]
                
                for op in stuck_operations:
                    op.state = CoherencyState.ERROR
                    op.error_message = "Operation timeout during recovery"
                    op.end_time = datetime.now()
                
                # 2. Reset state
                self._current_state = CoherencyState.COHERENT
                
                # 3. Clear impact manager history
                self.impact_manager.clear_history()
                
                # 4. Trigger cache maintenance if available
                if self.disk_cache:
                    try:
                        self.disk_cache.cleanup_cache()
                    except Exception as e:
                        logger.warning(f"Error during cache cleanup in recovery: {e}")
                
                logger.info("Cache coherency recovery completed")
                return True
                
        except Exception as e:
            logger.error(f"Error during cache coherency recovery: {e}")
            return False
    
    def get_operation_details(self, operation_id: str) -> Optional[Dict[str, Any]]:
        """Get details for a specific operation."""
        try:
            with self._lock:
                if operation_id not in self._operations:
                    return None
                
                op = self._operations[operation_id]
                duration = None
                if op.end_time:
                    duration = (op.end_time - op.start_time).total_seconds()
                
                return {
                    'operation_id': op.operation_id,
                    'type': op.operation_type,
                    'state': op.state.value,
                    'start_time': op.start_time.isoformat(),
                    'end_time': op.end_time.isoformat() if op.end_time else None,
                    'duration_seconds': duration,
                    'affected_systems': list(op.affected_systems),
                    'metadata': op.metadata,
                    'error_message': op.error_message
                }
                
        except Exception as e:
            logger.error(f"Error getting operation details: {e}")
            return None


# Global instance
_coherency_manager = None
_coherency_manager_lock = threading.Lock()


def get_coherency_manager(disk_cache=None, indicator_cache=None) -> CacheCoherencyManager:
    """Get global Cache Coherency Manager instance."""
    global _coherency_manager
    
    if _coherency_manager is None:
        with _coherency_manager_lock:
            if _coherency_manager is None:
                _coherency_manager = CacheCoherencyManager(disk_cache, indicator_cache)
    
    return _coherency_manager
