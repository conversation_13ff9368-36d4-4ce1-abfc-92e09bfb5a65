"""
Indicator Cache System for Smart Disk Cache.
Handles per-indicator caching with style separation.
"""

import json
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd

from ..config.logging_config import get_logger

logger = get_logger(__name__)


class IndicatorCache:
    """
    Indicator Cache System.
    Provides per-indicator caching with style data separation.
    """
    
    def __init__(self, disk_cache):
        """
        Initialize Indicator Cache.
        
        Args:
            disk_cache: SmartDiskCache instance
        """
        self.disk_cache = disk_cache
        self.cache_dir = disk_cache.cache_dir
        
        logger.debug("Indicator Cache initialized")
    
    def _generate_indicator_key(self, pair: str, timeframe: str, indicator_name: str, 
                              params: Dict[str, Any], data_cache_key: str) -> str:
        """Generate cache key for indicator data."""
        # Create deterministic key from parameters
        params_str = json.dumps(params, sort_keys=True)
        key_string = f"indicator_{pair}_{timeframe}_{indicator_name}_{params_str}_{data_cache_key}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _generate_style_key(self, indicator_cache_key: str) -> str:
        """Generate cache key for indicator style."""
        return f"style_{indicator_cache_key}"
    
    def store_indicator_data(self, pair: str, timeframe: str, indicator_name: str,
                           params: Dict[str, Any], data: pd.DataFrame,
                           data_cache_key: str) -> Optional[str]:
        """
        Store indicator calculation data.

        Args:
            pair: Currency pair
            timeframe: Timeframe
            indicator_name: Name of indicator
            params: Indicator parameters
            data: Calculated indicator data
            data_cache_key: Key of source data

        Returns:
            Cache key if successful, None otherwise
        """
        try:
            cache_key = self._generate_indicator_key(
                pair, timeframe, indicator_name, params, data_cache_key
            )

            if self.disk_cache.store_data(cache_key, data, 'indicators'):
                logger.debug(f"Indicator data stored: {indicator_name} for {pair} {timeframe}")
                return cache_key
            else:
                return None

        except Exception as e:
            logger.error(f"Error storing indicator data: {e}")
            return None

    def store_indicator_calculation(self, indicator_name: str, params: Dict[str, Any],
                                  data_cache_key: str, result: pd.DataFrame) -> Optional[str]:
        """
        Store indicator calculation results (compatibility method).

        Args:
            indicator_name: Name of indicator
            params: Indicator parameters
            data_cache_key: Key of source data
            result: Calculated indicator data

        Returns:
            Cache key if successful, None otherwise
        """
        # Extract pair and timeframe from data_cache_key if possible
        # For now, use placeholder values - this should be improved
        pair = "UNKNOWN"
        timeframe = "UNKNOWN"

        # Try to extract from data_cache_key format
        if "_" in data_cache_key:
            parts = data_cache_key.split("_")
            if len(parts) >= 3:
                pair = parts[1] if parts[1] else "UNKNOWN"
                timeframe = parts[2] if parts[2] else "UNKNOWN"

        return self.store_indicator_data(pair, timeframe, indicator_name, params, result, data_cache_key)
    
    def load_indicator_data(self, pair: str, timeframe: str, indicator_name: str,
                          params: Dict[str, Any], data_cache_key: str) -> Optional[pd.DataFrame]:
        """
        Load indicator calculation data.

        Args:
            pair: Currency pair
            timeframe: Timeframe
            indicator_name: Name of indicator
            params: Indicator parameters
            data_cache_key: Key of source data

        Returns:
            Indicator data if found, None otherwise
        """
        try:
            cache_key = self._generate_indicator_key(
                pair, timeframe, indicator_name, params, data_cache_key
            )

            data = self.disk_cache.load_data(cache_key, 'indicators')
            if data is not None:
                logger.debug(f"Indicator data loaded: {indicator_name} for {pair} {timeframe}")

            return data

        except Exception as e:
            logger.error(f"Error loading indicator data: {e}")
            return None

    def load_indicator_calculation(self, indicator_name: str, params: Dict[str, Any],
                                 data_cache_key: str) -> Optional[pd.DataFrame]:
        """
        Load indicator calculation results (compatibility method).

        Args:
            indicator_name: Name of indicator
            params: Indicator parameters
            data_cache_key: Key of source data

        Returns:
            Indicator data if found, None otherwise
        """
        # Extract pair and timeframe from data_cache_key if possible
        pair = "UNKNOWN"
        timeframe = "UNKNOWN"

        # Try to extract from data_cache_key format
        if "_" in data_cache_key:
            parts = data_cache_key.split("_")
            if len(parts) >= 3:
                pair = parts[1] if parts[1] else "UNKNOWN"
                timeframe = parts[2] if parts[2] else "UNKNOWN"

        return self.load_indicator_data(pair, timeframe, indicator_name, params, data_cache_key)
    
    def store_indicator_style(self, indicator_cache_key: str, style_config: Dict[str, Any]) -> bool:
        """
        Store indicator style configuration.
        
        Args:
            indicator_cache_key: Indicator cache key
            style_config: Style configuration
            
        Returns:
            True if successful, False otherwise
        """
        try:
            style_key = self._generate_style_key(indicator_cache_key)
            style_file = self.cache_dir / 'styles' / f"{style_key}.json"
            
            # Ensure directory exists
            style_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Store style as JSON
            with open(style_file, 'w') as f:
                json.dump(style_config, f, indent=2)
            
            logger.debug(f"Indicator style stored: {style_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing indicator style: {e}")
            return False
    
    def load_indicator_style(self, indicator_cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Load indicator style configuration.
        
        Args:
            indicator_cache_key: Indicator cache key
            
        Returns:
            Style configuration if found, None otherwise
        """
        try:
            style_key = self._generate_style_key(indicator_cache_key)
            style_file = self.cache_dir / 'styles' / f"{style_key}.json"
            
            if style_file.exists():
                with open(style_file, 'r') as f:
                    style_config = json.load(f)
                
                logger.debug(f"Indicator style loaded: {style_key}")
                return style_config
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error loading indicator style: {e}")
            return None
    
    def clear_indicator_cache(self, pair: str = None, timeframe: str = None, 
                            indicator_name: str = None) -> bool:
        """
        Clear indicator cache entries.
        
        Args:
            pair: Currency pair (optional filter)
            timeframe: Timeframe (optional filter)
            indicator_name: Indicator name (optional filter)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # This is a simplified implementation
            # In a full implementation, we would filter by the provided parameters
            indicators_dir = self.cache_dir / 'data' / 'indicators'
            styles_dir = self.cache_dir / 'styles'
            
            if indicators_dir.exists():
                for file_path in indicators_dir.glob('*.parquet'):
                    file_path.unlink()
            
            if styles_dir.exists():
                for file_path in styles_dir.glob('*.json'):
                    file_path.unlink()
            
            logger.info("Indicator cache cleared")
            return True

        except Exception as e:
            logger.error(f"Error clearing indicator cache: {e}")
            return False

    # ===== SMART INVALIDATION METHODS (Phase 5.5.2) =====

    def invalidate_indicator_smart(self, indicator_name: str, data_cache_key: str = None) -> int:
        """
        Smart invalidation for specific indicator.

        Args:
            indicator_name: Name of indicator to invalidate
            data_cache_key: Data cache key (optional)

        Returns:
            Number of cache entries removed
        """
        try:
            removed_count = 0
            indicators_dir = self.cache_dir / 'data' / 'indicators'
            styles_dir = self.cache_dir / 'styles'

            # Remove indicator data cache
            if indicators_dir.exists():
                pattern = f"*{indicator_name}*"
                if data_cache_key:
                    pattern = f"*{indicator_name}*{data_cache_key}*"

                for file_path in indicators_dir.glob(f"{pattern}.parquet"):
                    file_path.unlink()
                    removed_count += 1
                    logger.debug(f"Removed indicator cache: {file_path.name}")

            # Remove style cache
            if styles_dir.exists():
                for file_path in styles_dir.glob(f"*{indicator_name}*.json"):
                    file_path.unlink()
                    removed_count += 1
                    logger.debug(f"Removed style cache: {file_path.name}")

            logger.info(f"Smart invalidation for {indicator_name}: {removed_count} entries removed")
            return removed_count

        except Exception as e:
            logger.error(f"Error in smart indicator invalidation: {e}")
            return 0

    def invalidate_multiple_indicators_smart(self, indicator_names: List[str]) -> Dict[str, int]:
        """
        Smart invalidation for multiple indicators.

        Args:
            indicator_names: List of indicator names to invalidate

        Returns:
            Dictionary with removal counts per indicator
        """
        results = {}
        for indicator_name in indicator_names:
            results[indicator_name] = self.invalidate_indicator_smart(indicator_name)

        total_removed = sum(results.values())
        logger.info(f"Smart invalidation for {len(indicator_names)} indicators: {total_removed} total entries removed")
        return results

    def get_indicator_stats(self) -> Dict[str, Any]:
        """
        Get indicator cache statistics for smart invalidation.

        Returns:
            Dictionary with cache statistics
        """
        try:
            stats = {
                'total_indicators': 0,
                'total_styles': 0,
                'by_indicator': {},
                'cache_size_mb': 0
            }

            # Count indicator data files
            indicators_dir = self.cache_dir / 'data' / 'indicators'
            if indicators_dir.exists():
                indicator_files = list(indicators_dir.glob('*.parquet'))
                stats['total_indicators'] = len(indicator_files)

                # Calculate cache size
                total_size = sum(f.stat().st_size for f in indicator_files)
                stats['cache_size_mb'] = total_size / (1024 * 1024)

                # Group by indicator name (simplified)
                for file_path in indicator_files:
                    # Extract indicator name from filename (simplified)
                    filename = file_path.stem
                    indicator_name = filename.split('_')[1] if '_' in filename else 'unknown'
                    stats['by_indicator'][indicator_name] = stats['by_indicator'].get(indicator_name, 0) + 1

            # Count style files
            styles_dir = self.cache_dir / 'styles'
            if styles_dir.exists():
                style_files = list(styles_dir.glob('*.json'))
                stats['total_styles'] = len(style_files)

            return stats

        except Exception as e:
            logger.error(f"Error getting indicator stats: {e}")
            return {'error': str(e)}
