# Tahap 1
aku ingin membuat sebuat platform trading yang dapat melakukan backtesting dan live trading dengan ketentuan sebagai berikut :
pertama aku ingin menggunkan python serta library yang dibutuhkan dalam membuatnya.
kita akan bekerja pada direktori `streamtrade` untuk project ini jadi jangan buat file diluar direktori ini.

fitur-fitur yang mungkin ada :
1. data manager
2. chart viewer
3. indikator manager
4. strategi manager
5. backtesting
6. live trading
7. paper trading
8. risk management
9. reporting
10. optimization
11. deployment

untuk pertama kita akan membuat fitur-fitur yang berhubungan dengan backtesting, yaitu :
1. data manager
   a. data kita ambil dengan cara scanning direktori `histdata/MT/M1`
   b. pada direktori tersebut terdapat folder-folder dengan nama pair nya yang berisikan sub folder lagi dengan format tahun/file_tahunan.csv atau tahun/bulan/file_bulanan.csv
   c. silahkan cek struktur data pada direktori tersebut
   d. spesifikasi file csv dapat dilihat pada file `histdata/histdata.com_spec.md` data ini adalah data hasil download dari histdata.com
   e. saya ingin nanti dapat menggunakan data ini untuk chart viewer dan backtesting.
   f. namun mungkin masalahnya adalah data terlalu besar, perlu didapatkan solusi untuk ini. karena kemungkinan nanti akan ada penambahan data tahun-tahun sebelumnya dan data terbaru.
2. chart viewer dan indikator
   a. kita akan menggunakan library `plotly` (atau menurut anda yang terbagus) untuk membuat chart viewer, di utamakan yang dapat menghandle data yang besar dan dapat menampilkan indikator secara dinamis, serta dapat di kustomisasi.
   b. chart viewer ini akan menampilkan data dari data manager
   c. chart viewer ini akan menampilkan chart dari pair yang dipilih
   d. chart viewer ini akan menampilkan chart dari beberapa time frame yang dipilih, terkait dengan timeframe buat fungsi yang dapat otomatis mengkonversi dan validasi timeframe yang dipilih, karena mengingat kita hanya punya timeframe 1m dan kita tidak ingin mengkonversi ke dalam bentuk file.
   e. chart viewer ini akan menampilkan chart dari beberapa indikator yang dipilih dan dapat di toggle on/off, terkait dengan indikator buatlah fungsi yang dapat mengambil indikator dari library `ta-lib` atau `pandas-ta` atau `tulip` (atau menurut anda yang terbagus) dan dapat di konfigurasi sesuai dengan kebutuhan, misal indikator MACD dapat dikonfigurasi dengan fast, slow, signal, dll.
   f. indikator juga dapat kita buat custom indikator sesuai kebutuhan pada direktori khusus, dan buatkan class serta fungsi untuk mengambil indikator tersebut secara modular plug and play.
   g. setiap indikator akan mereturn variable dan menerima parameter input dari user sesuai dengan indikator yang kita buat, dan ini secara otomatis/dynamic tampil pada frontend/GUI
   h. pastikan semua indikator dapat tampil pada chart.
   i. chart perlu untuk ditampilkan secara dinamis ukurannya mengikuti parent element atau layar.
3. GUI/Frontend
   a. kita akan menggunakan library `streamlit` untuk membuat frontend, karena lebih mudah untuk membuat GUI dan dapat di deploy dengan mudah.
   b. pastikan semua fitur yang dibutuhkan sudah ada pada frontend, termasuk untuk melakukan konfigurasi dan menampilkan hasil output.
   c. pastikan semua fitur dapat diakses dengan mudah dan intuitif.

# Tahap 2

## 1. Timeframe Conversion Strategy

sebelumnya saya ingin mengerangkan bahwa tadi saya salah, default waktu/timezone data dari histdata.com adalat Eastern Standard Time (EST) time-zone WITHOUT Day Light Savings adjustments seperti yang ada pada file histdata.com_spec.md

Kemudian begini :
karena data kita adalah EST tanpa DST, maka offset 5 jam. Maka dari itu default Timezone kita adalah **UTC-5**.
- Buat Pengaturan **Timezone (Data)**, Default : **UTC-5**
- Buat Pengaturan **Timezone (Display)**, Default : **UTC+7** (Asia/Jakarta)
- Buat Pengaturan **Market Open (Forex)**, Default : **16:00**
- Buat Pengaturan **Market Open (Non Forex)**, Default : **17:00**
- mungkin kita juga perlu pengaturan **Non Forex Symbols**, Default : **XAUUSD, SPXUSD, NSXUSD** (kalau bisa multi select dengan display pairs yang tersedia)

dengan pengaturan di atas, sebenarnya kita tidak perlu konversi waktu pada data.
kita cukup gunakan data yang ada dengan menganggap bahwa data yang ada adalah sama dengan yang ada pada settingan kita di atas.

### jadi begini misal untuk settingan di atas **UTC-5**

#### 1. Forex dan market open 16:00 (**Untuk Forex**),
lalu dalam waktu sehari :
- Open  : 16:00 | candle 1m pertama adalah candle 16:00 kalau tidak ada GAP, jika ada pakai candle berikutnya  
- Close : 15:59 | candle 1m terakhir adalah candle 15:59 atau mungkin kurang, bisa jadi 15:44 karena GAP atau market tutup lebih cepat  

jadi candle–candle timeframe besarnya mengikuti, contoh :
candle 4H dalam sehari adalah  
1. 16:00 → 20:00 (candle 1m = 16:00 → 19:59), atau bisa saja 17:05–19:58 misalnya  
2. 20:00 → 00:00 (candle 1m = 20:00 → 23:59), sama seperti di atas jika ada gap  
3. 00:00 → 04:00 (candle 1m = 00:00 → 03:59), sama seperti di atas jika ada gap  
4. 04:00 → 08:00 (candle 1m = 04:00 → 07:59), sama seperti di atas jika ada gap  
5. 08:00 → 12:00 (candle 1m = 08:00 → 11:59), sama seperti di atas jika ada gap  
6. 12:00 → tutup (candle 1m = 12:00 → tutup), kenapa ini sampai tutup? bisa jadi hanya 3 jam isinya karena pasar tutup, Yang penting ketika 16:00 = hari baru  

kemudian untuk timeframe lainnya sama, mulainya dari 16:00  
misal daily dari 16:00 sampai selesai, atau weekly dari 16:00 hari Senin sampai selesai candle hari Jumat
jika data candle 1m "tidak ada (GAP)" maka sesuaikan dengan rentang waktu itu open dan close timeframe atasnya.

#### 2. Kemudian untuk pair non-forex dan market open 17:00, misalnya XAUUSD (gold) dan SPXUSD (S&P 500) dan NSXUSD (Nasdaq)  
kita pakai pengaturan "Non Forex Market Open" yang default 17:00, maka :
- Open  : 17:00 | candle 1m pertama adalah candle 17:00 kalau tidak ada pakai candle berikutnya  
- Close : 16:59 | candle 1m terakhir adalah candle 16:59 atau mungkin kurang, bisa jadi 16:44 karena GAP atau market tutup lebih cepat  

kemudian untuk timeframe besarnya mengikuti, contoh :
candle 4H dalam sehari adalah  
1. 17:00 -> 21:00 (candle 1m = 17:00 -> 20:59), atau bisa saja 17:05-20:58 misalnya  
2. 21:00 -> 01:00 (candle 1m = 21:00 -> 00:59), sama seperti yang di atas jika ada gap  
3. 01:00 -> 05:00 (candle 1m = 01:00 -> 04:59), sama seperti yang di atas jika ada gap  
4. 05:00 -> 09:00 (candle 1m = 05:00 -> 08:59), sama seperti yang di atas jika ada gap  
5. 09:00 -> 13:00 (candle 1m = 09:00 -> 12:59), sama seperti yang di atas jika ada gap  
6. 13:00 -> tutup (candle 1m = 13:00 -> tutup), kenapa ini sampai tutup? bisa jadi hanya 3 jam isinya karena pasar tutup, Yang penting ketika 17:00 = hari baru

kemudian untuk timeframe lainnya sama, mulainya dari 17:00  
misal daily dari 17:00 sampai selesai, atau weekly dari 17:00 hari Senin sampai selesai candle hari Jumat
jika data candle 1m "tidak ada (GAP)" maka sesuaikan dengan rentang waktu itu open dan close timeframe atasnya.


## 2. Insufficient Data Handling
buat **pengaturan** tentang ini,
"**Insufficient Data Behavior**" choice antara apakah "Load otomatis di background" atau "Tampilkan kosong dengan warning/pesan".

## 3. Cache persistence
- untuk cache sekarang kita pakai saja tanpa database, kita gunakan "**Smart Disk Cache**" `Parquet files + metadata index + LRU eviction`
- untuk LRU eviction pada settingan buat settingan "**Max Cache Size**" dengan default 10GB

## 4. Data Loading Strategy
saat ini kita punya 3 yaitu, **Last N Canldes, Date Range, All Available**
ganti yang Last N Candles jadi **N Days Back** dengan default 5 dan tidak boleh kurang dari 1

pada pengaturan user tadi tambahkan pengaturan :
- **Max Candles to Load** (number) default 200000
- **Max Canldes to Display** (Number) default 15000 (sebelumnya kita punya pengaturan hardcode max bar = 10000)
- **Eanble Timeframes** (checkbox/multi select) default [M1, M5, M15, H1, H4, D1], jadi TF M30, W1, MN1 tidak di gunakan secara default.
- **Cache all TF on load** (switch) default false, jika true, maka saat load, semua jenis timeframe yang di enable pada settingan di atas akan di cache.

### switch timeframe, load, konversi dan cache candle :
- user load data awal sebelumnya adalah 100 hari yang lalu dengan pilihan TF 4H
- load data 1m 100 hari yang lalu, cache data 1m, kemudian konversi ke 4H, lalu cache juga data TF 4H
- tampilkan data 4H pada chart dengan limit "max candles to display" yang ada pada pengaturan
- kemudian ketika user switch timeframe
- gunakan data 1m yang sudah ada di cache untuk konversi ke timeframe yang baru, lalu cache juga data TF baru tersebut
- kemudian tampilkan data pada chart dengan limit "max candles to display" yang ada pada pengaturan
- yang artinya settingan "max candles to display" ini berlaku untuk semua timeframe. dan berlaku untuk melilimit bar yang ditampilkan di chart, bukan yang di load dan di cache.
- sedangkan "max candles to load" ini berlaku untuk melimit jumlah data yang di load dari file, bukan yang di cache atau yang di tampilkan di chart.

## 5. Indikator cache strategy
karena kita menggunakan disk untuk cache, saya tidak tau ini bisa dilakukan atau tidak, namun saya ingin :
- saat user mengambahkan indikator maka kalkulasi dan cahe indikator ini dibuat terpisah per indikator.
- untuk data yang bersifat style (warna, garis, dll.) simpan di cache terpisah per indikator.
- kalkulasi dan cache ulang hanya indikator yang terdampak saja.
- begitu juga dengan saat menghapus, hanya indikator yang terdampak saja yang dihapus dari cache dan pastikan hapus cache kalkulasi dan style nya juga.
namun artinya cache data dan indikator harus terhubung agar misalnya ketika data dihapus maka semua indikator yang terdampak juga ikut terhapus dari cache.
saya belum tau apakah ini bisa dilakukan dengan menggunakan disk cache kita.
## 6. Pengaturan
karena kita tidak menggunakan database, maka semua pengaturan user disimpan pada sebuah file dengan format file dan caranya yang menurut anda yang terbaik.


## Istirahat - Tahapan Fix kecil kecil
- perlu cek dan experiment manual untuk candle H4, bentuk candle tidak sama dengan broker yang lain.
saya ingin tau sekarang perhitungan pembuatan candle di atas 1H, misal 1 H itu seperti apa dan dimana filenya, soalnya masih agak berbeda dengan yang di broker lain. walaupun memang benar tidak ada bentuk aneh dan gap seperti masalah sebelumnya.



# Next Steps (Optional)

Jika ingin enhancement lebih lanjut:
**Cache persistence**: Simpan cache ke disk
**Smart preloading**: Pre-load data untuk timeframes populer
**User preferences**: Simpan preferensi candle count per timeframe
**Progressive loading**: Load data bertahap untuk timeframes besar

Optimasi yang bisa dilakukan untuk indikator:
**Incremental caching**: Cache per indikator, bukan per kombinasi
**Lazy calculation**: Hitung indikator hanya saat dibutuhkan
**Smart invalidation**: Hanya recalculate yang affected