"""
Integration test for crosshair color fix.
Tests that crosshair colors are properly applied in actual chart creation.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from streamtrade.visualization.plotly_charts import PlotlyCharts
from streamtrade.config.settings import Settings


class TestCrosshairColorIntegration:
    """Integration test for crosshair color in actual charts."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data for testing."""
        dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
        np.random.seed(42)  # For reproducible results
        
        # Generate realistic price data
        base_price = 1.1000
        price_changes = np.random.normal(0, 0.0001, 100)
        close_prices = base_price + np.cumsum(price_changes)
        
        data = pd.DataFrame({
            'open': close_prices + np.random.normal(0, 0.00005, 100),
            'high': close_prices + np.abs(np.random.normal(0, 0.0001, 100)),
            'low': close_prices - np.abs(np.random.normal(0, 0.0001, 100)),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        return data
    
    def test_crosshair_color_in_chart_creation(self, sample_data):
        """Test that crosshair color is properly set in chart creation."""
        # Initialize PlotlyCharts
        charts = PlotlyCharts()
        
        # Create chart with crosshair enabled
        fig = charts.create_candlestick_chart(
            data=sample_data,
            title="Test Chart",
            show_vertical_line=True,
            show_horizontal_line=True
        )
        
        # Check that the figure was created successfully
        assert fig is not None
        assert len(fig.data) > 0
        
        # Check x-axis spike color (vertical crosshair)
        xaxis = fig.layout.xaxis
        assert xaxis.showspikes is True
        assert xaxis.spikecolor == "rgba(128,128,128,0.8)"
        assert xaxis.spikedash == "dot"
        assert xaxis.spikethickness == 1
        
        # Check y-axis spike color (horizontal crosshair)
        yaxis = fig.layout.yaxis
        assert yaxis.showspikes is True
        assert yaxis.spikecolor == "rgba(128,128,128,0.8)"
        assert yaxis.spikedash == "dot"
        assert yaxis.spikethickness == 1
    
    def test_crosshair_disabled(self, sample_data):
        """Test that crosshair can be properly disabled."""
        charts = PlotlyCharts()
        
        # Create chart with crosshair disabled
        fig = charts.create_candlestick_chart(
            data=sample_data,
            title="Test Chart",
            show_vertical_line=False,
            show_horizontal_line=False
        )
        
        # Check that crosshair is disabled
        xaxis = fig.layout.xaxis
        assert xaxis.showspikes is False
        assert xaxis.spikecolor is None
        
        yaxis = fig.layout.yaxis
        assert yaxis.showspikes is False
        assert yaxis.spikecolor is None
    
    def test_crosshair_partial_enable(self, sample_data):
        """Test that crosshair can be partially enabled (only vertical or horizontal)."""
        charts = PlotlyCharts()
        
        # Test only vertical crosshair
        fig_vertical = charts.create_candlestick_chart(
            data=sample_data,
            title="Test Chart",
            show_vertical_line=True,
            show_horizontal_line=False
        )
        
        xaxis = fig_vertical.layout.xaxis
        yaxis = fig_vertical.layout.yaxis
        
        assert xaxis.showspikes is True
        assert xaxis.spikecolor == "rgba(128,128,128,0.8)"
        assert yaxis.showspikes is False
        assert yaxis.spikecolor is None
        
        # Test only horizontal crosshair
        fig_horizontal = charts.create_candlestick_chart(
            data=sample_data,
            title="Test Chart",
            show_vertical_line=False,
            show_horizontal_line=True
        )
        
        xaxis = fig_horizontal.layout.xaxis
        yaxis = fig_horizontal.layout.yaxis
        
        assert xaxis.showspikes is False
        assert xaxis.spikecolor is None
        assert yaxis.showspikes is True
        assert yaxis.spikecolor == "rgba(128,128,128,0.8)"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
