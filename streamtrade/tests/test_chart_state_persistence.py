"""
Test chart state persistence functionality.
"""

import pytest
import json
import tempfile
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch
import pandas as pd
import numpy as np

from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.visualization.chart_viewer import ChartViewer


class TestChartStatePersistence:
    """Test chart state persistence across browser refreshes."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data for testing."""
        dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
        np.random.seed(42)
        
        base_price = 1.1000
        price_changes = np.random.normal(0, 0.0001, 100)
        close_prices = base_price + np.cumsum(price_changes)
        
        return pd.DataFrame({
            'open': close_prices + np.random.normal(0, 0.00005, 100),
            'high': close_prices + np.abs(np.random.normal(0, 0.0001, 100)),
            'low': close_prices - np.abs(np.random.normal(0, 0.0001, 100)),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
    
    def test_chart_state_file_creation(self):
        """Test that chart state file is created correctly."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir) / 'config'
            config_dir.mkdir(exist_ok=True)
            state_file = config_dir / 'last_chart_state.json'
            
            # Create test state
            state = {
                'pair': 'EURUSD',
                'timeframe': 'H1',
                'timestamp': datetime.now().isoformat(),
                'data_points': 100,
                'date_range': {'start': '2024-01-01', 'end': '2024-01-05'},
                'loading_method': 'n_days_back',
                'version': '1.0.0'
            }
            
            # Save state
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2)
            
            # Verify file exists and content is correct
            assert state_file.exists()
            
            with open(state_file, 'r', encoding='utf-8') as f:
                loaded_state = json.load(f)
            
            assert loaded_state['pair'] == 'EURUSD'
            assert loaded_state['timeframe'] == 'H1'
            assert loaded_state['version'] == '1.0.0'
    
    def test_enhanced_data_manager_get_cached_data(self, sample_data):
        """Test the get_cached_data method in EnhancedDataManager."""
        data_manager = EnhancedDataManager()
        
        # Add some data to cache
        cache_key = "EURUSD_H1_recent"
        data_manager._add_to_cache(cache_key, sample_data)
        
        # Test retrieval
        cached_data = data_manager.get_cached_data('EURUSD', 'H1')
        
        # Should find the data (though it might be limited by display candles)
        assert cached_data is not None
        assert len(cached_data) > 0
        assert 'open' in cached_data.columns
        assert 'high' in cached_data.columns
        assert 'low' in cached_data.columns
        assert 'close' in cached_data.columns
    
    def test_get_cached_data_not_found(self):
        """Test get_cached_data when no data is cached."""
        data_manager = EnhancedDataManager()
        
        # Try to get data that doesn't exist
        cached_data = data_manager.get_cached_data('GBPUSD', 'M5')
        
        # Should return None
        assert cached_data is None
    
    def test_chart_state_json_structure(self):
        """Test that chart state JSON has the expected structure."""
        state = {
            'pair': 'EURUSD',
            'timeframe': 'H1',
            'timestamp': datetime.now().isoformat(),
            'data_points': 100,
            'date_range': {
                'start': '2024-01-01T00:00:00',
                'end': '2024-01-05T23:59:59'
            },
            'loading_method': 'n_days_back',
            'version': '1.0.0'
        }
        
        # Verify required fields
        required_fields = ['pair', 'timeframe', 'timestamp', 'version']
        for field in required_fields:
            assert field in state
            assert state[field] is not None
        
        # Verify data types
        assert isinstance(state['pair'], str)
        assert isinstance(state['timeframe'], str)
        assert isinstance(state['timestamp'], str)
        assert isinstance(state['version'], str)
        
        # Verify timestamp format
        try:
            datetime.fromisoformat(state['timestamp'])
        except ValueError:
            pytest.fail("Timestamp is not in valid ISO format")
    
    def test_chart_viewer_cache_integration(self, sample_data):
        """Test that ChartViewer can work with cached data."""
        chart_viewer = ChartViewer()
        
        # Mock the data manager to return our sample data
        chart_viewer.data_manager.get_cached_data = Mock(return_value=sample_data)
        
        # Test that we can set current data
        chart_viewer.current_data = sample_data
        chart_viewer.current_pair = 'EURUSD'
        chart_viewer.current_timeframe = 'H1'
        
        # Verify state
        assert chart_viewer.current_pair == 'EURUSD'
        assert chart_viewer.current_timeframe == 'H1'
        assert chart_viewer.current_data is not None
        assert len(chart_viewer.current_data) == len(sample_data)
    
    def test_state_persistence_file_path(self):
        """Test that state file path is correctly constructed."""
        # This tests the path construction logic used in the actual implementation
        from pathlib import Path
        
        # Simulate the path construction from main_app.py
        config_dir = Path(__file__).parent.parent / 'config'
        state_file = config_dir / 'last_chart_state.json'
        
        # Verify path structure
        assert state_file.name == 'last_chart_state.json'
        assert state_file.parent.name == 'config'
        assert str(state_file).endswith('config/last_chart_state.json')
    
    def test_state_loading_with_missing_file(self):
        """Test state loading when file doesn't exist."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir) / 'config'
            state_file = config_dir / 'last_chart_state.json'
            
            # File doesn't exist
            assert not state_file.exists()
            
            # Should handle gracefully (no exception)
            try:
                if state_file.exists():
                    with open(state_file, 'r', encoding='utf-8') as f:
                        state = json.load(f)
                else:
                    state = None
                
                # Should be None when file doesn't exist
                assert state is None
                
            except Exception as e:
                pytest.fail(f"Should handle missing file gracefully: {e}")
    
    def test_state_loading_with_corrupted_file(self):
        """Test state loading when file is corrupted."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir) / 'config'
            config_dir.mkdir(exist_ok=True)
            state_file = config_dir / 'last_chart_state.json'
            
            # Create corrupted JSON file
            with open(state_file, 'w', encoding='utf-8') as f:
                f.write('{ invalid json content }')
            
            # Should handle gracefully
            try:
                with open(state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                pytest.fail("Should have raised JSON decode error")
            except json.JSONDecodeError:
                # Expected behavior
                pass
    
    def test_cache_key_generation_logic(self):
        """Test cache key generation patterns used in restoration."""
        pair = 'EURUSD'
        timeframe = 'H1'
        
        # Test the cache key patterns used in get_cached_data
        cache_keys = [
            f"{pair}_{timeframe}_recent",
            f"{pair}_{timeframe}_5d",
            f"{pair}_{timeframe}_10d",
            f"{pair}_{timeframe}_30d"
        ]
        
        expected_keys = [
            'EURUSD_H1_recent',
            'EURUSD_H1_5d',
            'EURUSD_H1_10d',
            'EURUSD_H1_30d'
        ]
        
        assert cache_keys == expected_keys


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
