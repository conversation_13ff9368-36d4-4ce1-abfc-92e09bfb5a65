#!/usr/bin/env python3
"""
Debug script to test browser refresh simulation with detailed logging.
"""

import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)

def test_browser_refresh_debug():
    """Debug browser refresh simulation with detailed logging."""
    print("🧪 Testing Browser Refresh with Debug Logging...")
    
    try:
        # Get file paths
        config_dir = Path(__file__).parent.parent / 'config'
        indicator_file = config_dir / 'last_indicator_state.json'
        chart_file = config_dir / 'last_chart_state.json'
        
        print(f"📁 Config directory: {config_dir}")
        print(f"📄 Indicator file: {indicator_file}")
        print(f"📄 Chart file: {chart_file}")
        
        # Step 1: Create initial state with indicators
        print("\n🔧 Step 1: Creating initial state with indicators...")
        chart_viewer1 = ChartViewer()
        
        # Load data
        success = chart_viewer1.load_data_n_days_back("EURUSD", "H1", 3)
        if not success:
            print("❌ Failed to load data")
            return False
        
        print(f"✅ Loaded {len(chart_viewer1.current_data)} candles")
        print(f"📊 Current state: {chart_viewer1.current_pair}/{chart_viewer1.current_timeframe}")
        
        # Add indicators
        print("\n📈 Adding indicators...")
        sma_success = chart_viewer1.add_indicator("SMA_20", "SMA", {"period": 20})
        ema_success = chart_viewer1.add_indicator("EMA_50", "EMA", {"period": 50})
        
        print(f"SMA added: {sma_success}")
        print(f"EMA added: {ema_success}")
        print(f"Active indicators: {len(chart_viewer1.current_indicators)}")
        
        # Check files created
        print(f"\n📄 Files after adding indicators:")
        print(f"Indicator file exists: {indicator_file.exists()}")
        print(f"Chart file exists: {chart_file.exists()}")
        
        if indicator_file.exists():
            with open(indicator_file, 'r') as f:
                indicator_data = json.load(f)
            print(f"Indicator file content: {indicator_data.get('pair')}/{indicator_data.get('timeframe')} with {len(indicator_data.get('indicators', {}))} indicators")
        
        if chart_file.exists():
            with open(chart_file, 'r') as f:
                chart_data = json.load(f)
            print(f"Chart file content: {chart_data.get('pair')}/{chart_data.get('timeframe')}")
        
        # Step 2: Simulate browser refresh
        print("\n🔄 Step 2: Simulating browser refresh...")
        print("Creating new ChartViewer instance (simulating fresh browser session)...")
        
        chart_viewer2 = ChartViewer()
        print(f"New instance created. Current state: {chart_viewer2.current_pair}/{chart_viewer2.current_timeframe}")
        
        # Step 3: Simulate chart restoration process
        print("\n🔧 Step 3: Simulating chart restoration process...")
        
        # Load same data (simulating cache restoration)
        print("Loading data (simulating cache restoration)...")
        success2 = chart_viewer2.load_data_n_days_back("EURUSD", "H1", 3)
        if not success2:
            print("❌ Failed to load data in new instance")
            return False
        
        print(f"✅ Data loaded in new instance: {len(chart_viewer2.current_data)} candles")
        print(f"📊 New instance state: {chart_viewer2.current_pair}/{chart_viewer2.current_timeframe}")
        
        # Check if indicators were automatically restored
        print(f"\n📊 Indicators after data loading:")
        print(f"Active indicators: {len(chart_viewer2.current_indicators)}")
        print(f"Indicator manager indicators: {len(chart_viewer2.indicator_manager.indicators)}")
        
        if chart_viewer2.current_indicators:
            print("✅ Indicators automatically restored!")
            for name, result in chart_viewer2.current_indicators.items():
                from_cache = result.metadata.get('from_cache', False)
                print(f"  - {name}: from_cache={from_cache}")
            return True
        else:
            print("⚠️ No indicators automatically restored")
            
            # Step 4: Manual restoration attempt
            print("\n🔧 Step 4: Manual restoration attempt...")
            
            # Check current state before manual restoration
            print(f"Before manual restoration:")
            print(f"  - Current pair: {chart_viewer2.current_pair}")
            print(f"  - Current timeframe: {chart_viewer2.current_timeframe}")
            print(f"  - Data cache key: {chart_viewer2.current_data_cache_key}")
            
            # Try manual restoration
            restored = chart_viewer2.load_indicator_state()
            print(f"Manual load_indicator_state() result: {restored}")
            
            if restored:
                print(f"Indicators in manager after restoration: {len(chart_viewer2.indicator_manager.indicators)}")
                
                # Recalculate indicators
                print("Recalculating indicators...")
                chart_viewer2._calculate_indicators()
                
                print(f"Active indicators after recalculation: {len(chart_viewer2.current_indicators)}")
                
                if chart_viewer2.current_indicators:
                    print("✅ Manual restoration successful!")
                    for name, result in chart_viewer2.current_indicators.items():
                        from_cache = result.metadata.get('from_cache', False)
                        print(f"  - {name}: from_cache={from_cache}")
                    return True
                else:
                    print("❌ Manual restoration failed - no active indicators after recalculation")
            else:
                print("❌ Manual restoration failed - load_indicator_state returned False")
                
                # Debug: Check file content vs current state
                print("\n🔍 Debug: File vs Current State Analysis")
                if indicator_file.exists():
                    with open(indicator_file, 'r') as f:
                        file_data = json.load(f)
                    
                    print(f"File pair/timeframe: {file_data.get('pair')}/{file_data.get('timeframe')}")
                    print(f"Current pair/timeframe: {chart_viewer2.current_pair}/{chart_viewer2.current_timeframe}")
                    print(f"Match: {file_data.get('pair') == chart_viewer2.current_pair and file_data.get('timeframe') == chart_viewer2.current_timeframe}")
                    
                    if file_data.get('pair') != chart_viewer2.current_pair or file_data.get('timeframe') != chart_viewer2.current_timeframe:
                        print("❌ Pair/timeframe mismatch detected!")
                        print("This is why indicators are not being restored.")
                else:
                    print("❌ Indicator file does not exist!")
            
            return False
            
    except Exception as e:
        logger.error(f"Error in browser refresh debug test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run browser refresh debug test."""
    print("🚀 Browser Refresh Debug Test")
    print("=" * 60)
    
    # Run test
    test_result = test_browser_refresh_debug()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"  Browser Refresh Debug: {'✅ PASS' if test_result else '❌ FAIL'}")
    
    print(f"\n🎯 Overall Result: {'✅ TEST PASSED' if test_result else '❌ TEST FAILED'}")
    
    if test_result:
        print("\n🎉 Browser refresh simulation is working correctly!")
    else:
        print("\n🔧 Browser refresh simulation needs debugging.")
        print("Check the logs above for detailed analysis.")
    
    return test_result

if __name__ == "__main__":
    main()
