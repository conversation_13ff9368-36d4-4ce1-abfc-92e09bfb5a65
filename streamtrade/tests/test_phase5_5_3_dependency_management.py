"""
Test suite for Phase 5.5.3: Indicator Dependency Management System.
Tests dependency graph, cascade calculations, and optimal ordering.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.cache.dependency_manager import (
    DependencyManager, DependencyGraph, DependencyRelation, DependencyType
)
from streamtrade.indicators.indicator_manager import IndicatorManager
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)


class TestDependencyGraph(unittest.TestCase):
    """Test Dependency Graph functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.graph = DependencyGraph()
    
    def test_add_dependency(self):
        """Test adding dependencies to the graph."""
        relation = DependencyRelation(
            dependent='MACD_Signal',
            dependency='MACD',
            dependency_type=DependencyType.DERIVED,
            parameters={'signal_period': 9}
        )
        
        self.graph.add_dependency(relation)
        
        # Check that dependency was added
        deps = self.graph.get_dependencies('MACD_Signal')
        self.assertEqual(deps, {'MACD'})
        
        dependents = self.graph.get_dependents('MACD')
        self.assertEqual(dependents, {'MACD_Signal'})
    
    def test_circular_dependency_detection(self):
        """Test circular dependency detection."""
        # Add A -> B
        relation1 = DependencyRelation(
            dependent='A',
            dependency='B',
            dependency_type=DependencyType.DIRECT,
            parameters={}
        )
        self.graph.add_dependency(relation1)
        
        # Try to add B -> A (should fail)
        relation2 = DependencyRelation(
            dependent='B',
            dependency='A',
            dependency_type=DependencyType.DIRECT,
            parameters={}
        )
        
        with self.assertRaises(ValueError):
            self.graph.add_dependency(relation2)
    
    def test_topological_sort(self):
        """Test topological sorting for calculation order."""
        # Create dependency chain: A -> B -> C
        relations = [
            DependencyRelation('B', 'A', DependencyType.DIRECT, {}),
            DependencyRelation('C', 'B', DependencyType.DIRECT, {})
        ]
        
        for relation in relations:
            self.graph.add_dependency(relation)
        
        # Get calculation order
        order = self.graph.get_calculation_order({'A', 'B', 'C'})
        
        # A should come before B, B should come before C
        self.assertEqual(order.index('A'), 0)
        self.assertTrue(order.index('A') < order.index('B'))
        self.assertTrue(order.index('B') < order.index('C'))
    
    def test_complex_dependency_graph(self):
        """Test complex dependency graph with multiple relationships."""
        # Create complex graph:
        # SMA -> BB_Upper, BB_Lower
        # MACD -> MACD_Signal -> MACD_Histogram
        # RSI (independent)
        
        relations = [
            DependencyRelation('BB_Upper', 'SMA', DependencyType.OVERLAY, {}),
            DependencyRelation('BB_Lower', 'SMA', DependencyType.OVERLAY, {}),
            DependencyRelation('MACD_Signal', 'MACD', DependencyType.DERIVED, {}),
            DependencyRelation('MACD_Histogram', 'MACD', DependencyType.DERIVED, {}),
            DependencyRelation('MACD_Histogram', 'MACD_Signal', DependencyType.DERIVED, {})
        ]
        
        for relation in relations:
            self.graph.add_dependency(relation)
        
        indicators = {'SMA', 'BB_Upper', 'BB_Lower', 'MACD', 'MACD_Signal', 'MACD_Histogram', 'RSI'}
        order = self.graph.get_calculation_order(indicators)
        
        # Verify dependencies are respected
        self.assertTrue(order.index('SMA') < order.index('BB_Upper'))
        self.assertTrue(order.index('SMA') < order.index('BB_Lower'))
        self.assertTrue(order.index('MACD') < order.index('MACD_Signal'))
        self.assertTrue(order.index('MACD') < order.index('MACD_Histogram'))
        self.assertTrue(order.index('MACD_Signal') < order.index('MACD_Histogram'))
    
    def test_get_all_dependencies(self):
        """Test getting all dependencies (direct and indirect)."""
        # Create chain: A -> B -> C -> D
        relations = [
            DependencyRelation('B', 'A', DependencyType.DIRECT, {}),
            DependencyRelation('C', 'B', DependencyType.DIRECT, {}),
            DependencyRelation('D', 'C', DependencyType.DIRECT, {})
        ]
        
        for relation in relations:
            self.graph.add_dependency(relation)
        
        # D should depend on A, B, C
        all_deps = self.graph.get_all_dependencies('D')
        self.assertEqual(all_deps, {'A', 'B', 'C'})
        
        # B should depend only on A
        all_deps_b = self.graph.get_all_dependencies('B')
        self.assertEqual(all_deps_b, {'A'})
    
    def test_get_all_dependents(self):
        """Test getting all dependents (direct and indirect)."""
        # Create chain: A -> B -> C -> D
        relations = [
            DependencyRelation('B', 'A', DependencyType.DIRECT, {}),
            DependencyRelation('C', 'B', DependencyType.DIRECT, {}),
            DependencyRelation('D', 'C', DependencyType.DIRECT, {})
        ]
        
        for relation in relations:
            self.graph.add_dependency(relation)
        
        # A should have B, C, D as dependents
        all_dependents = self.graph.get_all_dependents('A')
        self.assertEqual(all_dependents, {'B', 'C', 'D'})
        
        # C should have only D as dependent
        all_dependents_c = self.graph.get_all_dependents('C')
        self.assertEqual(all_dependents_c, {'D'})


class TestDependencyManager(unittest.TestCase):
    """Test Dependency Manager functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.dependency_manager = DependencyManager()
    
    def test_predefined_dependencies(self):
        """Test predefined dependency initialization."""
        # Check that predefined dependencies are loaded
        self.assertIn('MACD_Signal', self.dependency_manager._predefined_dependencies)
        self.assertIn('MACD_Histogram', self.dependency_manager._predefined_dependencies)
        self.assertIn('Stochastic_D', self.dependency_manager._predefined_dependencies)
    
    def test_add_remove_dependency(self):
        """Test adding and removing dependencies."""
        # Add dependency
        success = self.dependency_manager.add_indicator_dependency(
            'EMA_Signal', 'EMA', DependencyType.DERIVED
        )
        self.assertTrue(success)
        
        # Check dependency exists
        deps = self.dependency_manager.graph.get_dependencies('EMA_Signal')
        self.assertEqual(deps, {'EMA'})
        
        # Remove dependency
        success = self.dependency_manager.remove_indicator_dependency('EMA_Signal', 'EMA')
        self.assertTrue(success)
        
        # Check dependency removed
        deps = self.dependency_manager.graph.get_dependencies('EMA_Signal')
        self.assertEqual(deps, set())
    
    def test_cascade_indicators(self):
        """Test cascade indicator calculation."""
        # Add dependencies: A -> B -> C
        self.dependency_manager.add_indicator_dependency('B', 'A', DependencyType.DIRECT)
        self.dependency_manager.add_indicator_dependency('C', 'B', DependencyType.DIRECT)
        
        # When A changes, B and C should be in cascade
        cascade = self.dependency_manager.get_cascade_indicators('A')
        self.assertEqual(cascade, {'A', 'B', 'C'})
        
        # When B changes, only C should be in cascade (plus B itself)
        cascade_b = self.dependency_manager.get_cascade_indicators('B')
        self.assertEqual(cascade_b, {'B', 'C'})
        
        # When C changes, only C should be in cascade
        cascade_c = self.dependency_manager.get_cascade_indicators('C')
        self.assertEqual(cascade_c, {'C'})
    
    def test_auto_detect_dependencies(self):
        """Test auto-detection of dependencies."""
        # Create indicator configs that match predefined dependencies
        indicator_configs = {
            'MACD': {'type': 'MACD', 'params': {}},
            'MACD_Signal': {'type': 'MACD_Signal', 'params': {}},
            'SMA': {'type': 'SMA', 'params': {'period': 20}},
            'BB_Upper': {'type': 'BB_Upper', 'params': {}}
        }
        
        detected_count = self.dependency_manager.auto_detect_dependencies(indicator_configs)
        
        # Should detect at least MACD_Signal -> MACD dependency
        self.assertGreater(detected_count, 0)
        
        # Check that MACD_Signal depends on MACD
        deps = self.dependency_manager.graph.get_dependencies('MACD_Signal')
        self.assertIn('MACD', deps)
    
    def test_calculation_order(self):
        """Test calculation order generation."""
        # Add some dependencies
        self.dependency_manager.add_indicator_dependency('B', 'A', DependencyType.DIRECT)
        self.dependency_manager.add_indicator_dependency('C', 'A', DependencyType.DIRECT)
        self.dependency_manager.add_indicator_dependency('D', 'B', DependencyType.DIRECT)
        
        indicators = {'A', 'B', 'C', 'D'}
        order = self.dependency_manager.get_calculation_order(indicators)
        
        # A should come first
        self.assertEqual(order[0], 'A')
        # B and C should come after A
        self.assertTrue(order.index('A') < order.index('B'))
        self.assertTrue(order.index('A') < order.index('C'))
        # D should come after B
        self.assertTrue(order.index('B') < order.index('D'))


class TestIndicatorManagerDependencies(unittest.TestCase):
    """Test Indicator Manager with dependency management."""
    
    def setUp(self):
        """Set up test environment."""
        self.indicator_manager = IndicatorManager()
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='1h')
        self.sample_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.5, 2.5, 100),
            'low': np.random.uniform(0.5, 1.5, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        }, index=dates)
        
        # Ensure OHLC logic
        for i in range(len(self.sample_data)):
            high = max(self.sample_data.iloc[i]['open'], self.sample_data.iloc[i]['close'])
            low = min(self.sample_data.iloc[i]['open'], self.sample_data.iloc[i]['close'])
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('high')] = max(high, self.sample_data.iloc[i]['high'])
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('low')] = min(low, self.sample_data.iloc[i]['low'])
    
    def test_dependency_methods_exist(self):
        """Test that dependency management methods exist."""
        self.assertTrue(hasattr(self.indicator_manager, 'add_indicator_dependency'))
        self.assertTrue(hasattr(self.indicator_manager, 'remove_indicator_dependency'))
        self.assertTrue(hasattr(self.indicator_manager, 'get_indicator_dependencies'))
        self.assertTrue(hasattr(self.indicator_manager, 'get_indicator_dependents'))
        self.assertTrue(hasattr(self.indicator_manager, 'calculate_with_dependencies'))
        self.assertTrue(hasattr(self.indicator_manager, 'calculate_cascade'))
    
    def test_add_remove_dependencies(self):
        """Test adding and removing dependencies through indicator manager."""
        # Add indicators first
        self.indicator_manager.add_indicator('SMA_20', 'SMA', {'period': 20})
        self.indicator_manager.add_indicator('EMA_20', 'EMA', {'period': 20})
        
        # Add dependency
        success = self.indicator_manager.add_indicator_dependency('EMA_20', 'SMA_20')
        self.assertTrue(success)
        
        # Check dependency
        deps = self.indicator_manager.get_indicator_dependencies('EMA_20')
        self.assertEqual(deps, {'SMA_20'})
        
        # Remove dependency
        success = self.indicator_manager.remove_indicator_dependency('EMA_20', 'SMA_20')
        self.assertTrue(success)
        
        # Check dependency removed
        deps = self.indicator_manager.get_indicator_dependencies('EMA_20')
        self.assertEqual(deps, set())
    
    def test_calculation_with_dependencies(self):
        """Test calculation with dependency ordering."""
        # Add indicators
        self.indicator_manager.add_indicator('SMA_20', 'SMA', {'period': 20})
        self.indicator_manager.add_indicator('RSI_14', 'RSI', {'period': 14})
        
        # Calculate with dependencies
        results = self.indicator_manager.calculate_with_dependencies(self.sample_data)
        
        # Should have results for both indicators
        self.assertIn('SMA_20', results)
        self.assertIn('RSI_14', results)
    
    def test_auto_detect_dependencies(self):
        """Test auto-detection of dependencies."""
        # Add indicators that have predefined dependencies
        self.indicator_manager.add_indicator('SMA_20', 'SMA', {'period': 20})
        
        # Auto-detect dependencies
        detected_count = self.indicator_manager.auto_detect_dependencies()
        
        # Should return count (may be 0 if no matching predefined dependencies)
        self.assertIsInstance(detected_count, int)
        self.assertGreaterEqual(detected_count, 0)


if __name__ == '__main__':
    # Configure logging for tests
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    unittest.main(verbosity=2)
