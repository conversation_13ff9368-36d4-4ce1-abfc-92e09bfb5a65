"""
Test to verify that the timeframe switcher has been removed from the sidebar.
"""

import pytest
import inspect
from streamtrade.gui.components.data_selector import DataSelector
from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.config.strings import strings


class TestSidebarTimeframeRemoval:
    """Test that timeframe switcher is removed from sidebar."""
    
    def test_quick_timeframe_switch_string_removed(self):
        """Test that quick_timeframe_switch string is removed from strings config."""
        # Check that the string is no longer in DATA_SELECTION
        assert "quick_timeframe_switch" not in strings.DATA_SELECTION
        
        # Verify other strings are still present
        assert "load_data" in strings.DATA_SELECTION
        assert "timeframe" in strings.DATA_SELECTION
        assert "currency_pair" in strings.DATA_SELECTION
    
    def test_data_selector_render_method_no_timeframe_switcher(self):
        """Test that DataSelector.render() method doesn't contain timeframe switcher code."""
        # Get the source code of the render method
        chart_viewer = ChartViewer()
        data_selector = DataSelector(chart_viewer)
        render_source = inspect.getsource(data_selector.render)
        
        # Check that timeframe switcher related code is not present
        assert "quick_timeframe_switch" not in render_source
        assert "Quick timeframe switcher" not in render_source
        assert "st.columns(len(available_common))" not in render_source
        assert "change_timeframe" not in render_source
        
        # Verify that essential functionality is still present
        assert "load_data" in render_source
        assert "selected_pair" in render_source
        assert "selected_timeframe" in render_source
    
    def test_data_selector_initialization(self):
        """Test that DataSelector can still be initialized properly."""
        chart_viewer = ChartViewer()
        data_selector = DataSelector(chart_viewer)
        
        # Verify initialization
        assert data_selector.chart_viewer is not None
        assert data_selector.user_settings is not None
        assert hasattr(data_selector, 'render')
    
    def test_render_method_returns_correct_structure(self):
        """Test that render method still returns the expected data structure."""
        chart_viewer = ChartViewer()
        data_selector = DataSelector(chart_viewer)
        
        # Mock streamlit session state for testing
        import streamlit as st
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self.last_selected_pair = None
                    self.last_selected_timeframe = None
                    self.data_loaded = False
                
                def __setattr__(self, name, value):
                    object.__setattr__(self, name, value)
                
                def __getattr__(self, name):
                    return getattr(self, name, None)
            
            st.session_state = MockSessionState()
        
        # The render method should still work (though it will fail in test environment
        # due to streamlit components, we can at least verify the method exists)
        assert callable(data_selector.render)
    
    def test_timeframe_functionality_moved_to_chart_component(self):
        """Test that timeframe switching is still available in chart component."""
        from streamtrade.gui.components.chart_component import ChartComponent

        # Get the source code of ChartComponent
        chart_viewer = ChartViewer()
        chart_component = ChartComponent(chart_viewer)
        render_source = inspect.getsource(chart_component._render_chart_controls)

        # Verify timeframe selector is still in chart component
        assert "timeframe" in render_source.lower()
        assert "selectbox" in render_source
        assert "change_timeframe" in render_source


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
