#!/usr/bin/env python3
"""
Test script to verify that last_indicator_state.json updates when indicators are added/removed.
"""

import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)

def get_file_timestamp(file_path):
    """Get file modification timestamp."""
    if file_path.exists():
        return file_path.stat().st_mtime
    return None

def read_indicator_file(file_path):
    """Read and parse indicator state file."""
    if file_path.exists():
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error reading file: {e}")
    return None

def test_file_updates_on_indicator_changes():
    """Test if file updates when indicators are added/removed."""
    print("🧪 Testing File Updates on Indicator Changes...")
    
    try:
        # Get file path
        config_dir = Path(__file__).parent.parent / 'config'
        indicator_file = config_dir / 'last_indicator_state.json'
        
        # Clean up existing file
        if indicator_file.exists():
            indicator_file.unlink()
            print("🧹 Cleaned up existing indicator state file")
        
        # Initialize chart viewer
        chart_viewer = ChartViewer()
        
        # Load data
        print("📊 Loading data...")
        success = chart_viewer.load_data_n_days_back("EURUSD", "H1", 3)
        if not success:
            print("❌ Failed to load data")
            return False
        
        print(f"✅ Loaded {len(chart_viewer.current_data)} candles")
        
        # Test 1: Add first indicator
        print("\n📈 Test 1: Adding first indicator (SMA_20)...")
        initial_timestamp = get_file_timestamp(indicator_file)
        print(f"Initial file timestamp: {initial_timestamp}")
        
        success1 = chart_viewer.add_indicator(
            name="SMA_20",
            indicator_type="SMA",
            parameters={"period": 20}
        )
        
        time.sleep(0.1)  # Small delay to ensure file write
        
        after_add1_timestamp = get_file_timestamp(indicator_file)
        after_add1_data = read_indicator_file(indicator_file)
        
        print(f"After add SMA_20:")
        print(f"  - Success: {success1}")
        print(f"  - File timestamp: {after_add1_timestamp}")
        print(f"  - File exists: {indicator_file.exists()}")
        if after_add1_data:
            print(f"  - Indicators in file: {len(after_add1_data.get('indicators', {}))}")
            print(f"  - Indicator names: {list(after_add1_data.get('indicators', {}).keys())}")
        
        # Test 2: Add second indicator
        print("\n📈 Test 2: Adding second indicator (EMA_50)...")
        before_add2_timestamp = after_add1_timestamp
        
        success2 = chart_viewer.add_indicator(
            name="EMA_50",
            indicator_type="EMA",
            parameters={"period": 50}
        )
        
        time.sleep(0.1)  # Small delay to ensure file write
        
        after_add2_timestamp = get_file_timestamp(indicator_file)
        after_add2_data = read_indicator_file(indicator_file)
        
        print(f"After add EMA_50:")
        print(f"  - Success: {success2}")
        print(f"  - File timestamp: {after_add2_timestamp}")
        print(f"  - File updated: {after_add2_timestamp != before_add2_timestamp}")
        if after_add2_data:
            print(f"  - Indicators in file: {len(after_add2_data.get('indicators', {}))}")
            print(f"  - Indicator names: {list(after_add2_data.get('indicators', {}).keys())}")
        
        # Test 3: Remove indicator
        print("\n📈 Test 3: Removing indicator (SMA_20)...")
        before_remove_timestamp = after_add2_timestamp
        
        success3 = chart_viewer.remove_indicator("SMA_20")
        
        time.sleep(0.1)  # Small delay to ensure file write
        
        after_remove_timestamp = get_file_timestamp(indicator_file)
        after_remove_data = read_indicator_file(indicator_file)
        
        print(f"After remove SMA_20:")
        print(f"  - Success: {success3}")
        print(f"  - File timestamp: {after_remove_timestamp}")
        print(f"  - File updated: {after_remove_timestamp != before_remove_timestamp}")
        if after_remove_data:
            print(f"  - Indicators in file: {len(after_remove_data.get('indicators', {}))}")
            print(f"  - Indicator names: {list(after_remove_data.get('indicators', {}).keys())}")
        
        # Test 4: Toggle indicator
        print("\n📈 Test 4: Toggling indicator (EMA_50)...")
        before_toggle_timestamp = after_remove_timestamp
        
        success4 = chart_viewer.toggle_indicator("EMA_50", False)  # Disable
        
        time.sleep(0.1)  # Small delay to ensure file write
        
        after_toggle_timestamp = get_file_timestamp(indicator_file)
        after_toggle_data = read_indicator_file(indicator_file)
        
        print(f"After toggle EMA_50 (disable):")
        print(f"  - Success: {success4}")
        print(f"  - File timestamp: {after_toggle_timestamp}")
        print(f"  - File updated: {after_toggle_timestamp != before_toggle_timestamp}")
        if after_toggle_data:
            ema_config = after_toggle_data.get('indicators', {}).get('EMA_50', {})
            print(f"  - EMA_50 enabled: {ema_config.get('enabled', True)}")
        
        # Validation
        print("\n🔍 Validation:")
        
        # Check if file was created
        file_created = indicator_file.exists()
        print(f"✅ File created: {file_created}")
        
        # Check if file updated on each operation
        timestamps_different = (
            after_add1_timestamp != initial_timestamp and
            after_add2_timestamp != after_add1_timestamp and
            after_remove_timestamp != after_add2_timestamp and
            after_toggle_timestamp != after_remove_timestamp
        )
        print(f"✅ File updated on each operation: {timestamps_different}")
        
        # Check if final state is correct
        final_data = read_indicator_file(indicator_file)
        if final_data:
            final_indicators = final_data.get('indicators', {})
            has_ema_only = len(final_indicators) == 1 and 'EMA_50' in final_indicators
            ema_disabled = final_indicators.get('EMA_50', {}).get('enabled', True) == False
            print(f"✅ Final state correct (EMA_50 only, disabled): {has_ema_only and ema_disabled}")
        else:
            print("❌ Final state incorrect: No data in file")
            return False
        
        # Overall success
        overall_success = (
            success1 and success2 and success3 and success4 and
            file_created and timestamps_different and
            has_ema_only and ema_disabled
        )
        
        return overall_success
        
    except Exception as e:
        logger.error(f"Error in file update test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run file update test."""
    print("🚀 File Update on Indicator Changes Test")
    print("=" * 60)
    
    # Run test
    test_result = test_file_updates_on_indicator_changes()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"  File Update Test: {'✅ PASS' if test_result else '❌ FAIL'}")
    
    print(f"\n🎯 Overall Result: {'✅ TEST PASSED' if test_result else '❌ TEST FAILED'}")
    
    if test_result:
        print("\n🎉 File updates are working correctly!")
        print("📌 Key Features Validated:")
        print("   - File created when first indicator added")
        print("   - File updated when indicators added/removed/toggled")
        print("   - File content reflects current indicator state")
        print("   - Timestamps change on each operation")
    else:
        print("\n🔧 File update mechanism needs further fixes.")
    
    return test_result

if __name__ == "__main__":
    main()
