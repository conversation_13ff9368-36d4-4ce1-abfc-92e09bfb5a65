#!/usr/bin/env python3
"""
Test script for Persistent Indicator State
Tests if indicators persist after browser refresh using persistent file storage.
"""

import sys
import os
import pandas as pd
import json
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)

def test_persistent_indicator_file():
    """Test if indicator state is saved to and loaded from persistent file."""
    print("🧪 Testing Persistent Indicator File Storage...")
    
    try:
        # Clean up any existing state files
        config_dir = Path(__file__).parent.parent / 'config'
        indicator_file = config_dir / 'last_indicator_state.json'
        if indicator_file.exists():
            indicator_file.unlink()
            print("🧹 Cleaned up existing indicator state file")
        
        # Initialize chart viewer
        chart_viewer = ChartViewer()
        
        # Load some data
        print("📊 Loading data...")
        success = chart_viewer.load_data_n_days_back("EURUSD", "H1", 3)
        if not success:
            print("❌ Failed to load data")
            return False
        
        print(f"✅ Loaded {len(chart_viewer.current_data)} candles")
        
        # Add indicators
        print("\n📈 Adding indicators...")
        
        sma_success = chart_viewer.add_indicator(
            name="SMA_20",
            indicator_type="SMA",
            parameters={"period": 20}
        )
        
        ema_success = chart_viewer.add_indicator(
            name="EMA_50",
            indicator_type="EMA", 
            parameters={"period": 50}
        )
        
        print(f"SMA added: {sma_success}")
        print(f"EMA added: {ema_success}")
        
        # Check if persistent file was created
        if indicator_file.exists():
            print("✅ Persistent indicator state file created")
            
            # Read and verify file content
            with open(indicator_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            print(f"📋 File contains:")
            print(f"  - Pair: {state_data.get('pair')}")
            print(f"  - Timeframe: {state_data.get('timeframe')}")
            print(f"  - Indicators: {len(state_data.get('indicators', {}))}")
            print(f"  - Timestamp: {state_data.get('timestamp')}")
            
            # Verify indicator data
            indicators = state_data.get('indicators', {})
            for name, config in indicators.items():
                print(f"    - {name}: {config['indicator_type']} with params {config['parameters']}")
            
            return True
        else:
            print("❌ Persistent indicator state file not created")
            return False
            
    except Exception as e:
        logger.error(f"Error in persistent indicator file test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def test_browser_refresh_simulation():
    """Test browser refresh simulation by creating new chart viewer instance."""
    print("\n🧪 Testing Browser Refresh Simulation...")
    
    try:
        # Ensure we have a persistent state file from previous test
        config_dir = Path(__file__).parent.parent / 'config'
        indicator_file = config_dir / 'last_indicator_state.json'
        
        if not indicator_file.exists():
            print("⚠️ No persistent indicator state file found, creating one first...")
            # Run the first test to create the file
            if not test_persistent_indicator_file():
                print("❌ Failed to create persistent state file")
                return False
        
        # Simulate browser refresh by creating new chart viewer
        print("🔄 Simulating browser refresh with new ChartViewer instance...")
        chart_viewer_new = ChartViewer()
        
        # Load same data (simulating chart restoration)
        success = chart_viewer_new.load_data_n_days_back("EURUSD", "H1", 3)
        if not success:
            print("❌ Failed to load data in new instance")
            return False
        
        print(f"✅ Loaded {len(chart_viewer_new.current_data)} candles in new instance")
        
        # Check if indicators were restored
        print(f"📊 Active indicators before restoration: {len(chart_viewer_new.current_indicators)}")
        
        # The load_data_n_days_back should have called load_indicator_state internally
        # Let's check if indicators were restored
        if len(chart_viewer_new.current_indicators) > 0:
            print(f"✅ Indicators automatically restored: {len(chart_viewer_new.current_indicators)}")
            
            for name, result in chart_viewer_new.current_indicators.items():
                from_cache = result.metadata.get('from_cache', False)
                category = result.metadata.get('category', 'UNKNOWN')
                print(f"  - {name}: from_cache={from_cache}, category={category}")
            
            return True
        else:
            print("⚠️ No indicators restored automatically, trying manual restoration...")
            
            # Try manual restoration
            restored = chart_viewer_new.load_indicator_state()
            if restored:
                # Recalculate indicators
                chart_viewer_new._calculate_indicators()
                
                print(f"✅ Manual restoration successful: {len(chart_viewer_new.current_indicators)} indicators")
                return True
            else:
                print("❌ Manual restoration also failed")
                return False
            
    except Exception as e:
        logger.error(f"Error in browser refresh simulation: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def test_state_file_content():
    """Test the content and structure of the persistent state file."""
    print("\n🧪 Testing State File Content Structure...")
    
    try:
        config_dir = Path(__file__).parent.parent / 'config'
        indicator_file = config_dir / 'last_indicator_state.json'
        
        if not indicator_file.exists():
            print("❌ No persistent indicator state file found")
            return False
        
        with open(indicator_file, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        # Validate required fields
        required_fields = ['indicators', 'pair', 'timeframe', 'timestamp']
        missing_fields = [field for field in required_fields if field not in state_data]
        
        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            return False
        
        # Validate indicators structure
        indicators = state_data.get('indicators', {})
        if not indicators:
            print("❌ No indicators found in state file")
            return False
        
        for name, config in indicators.items():
            required_indicator_fields = ['indicator_type', 'parameters', 'enabled']
            missing_indicator_fields = [field for field in required_indicator_fields if field not in config]
            
            if missing_indicator_fields:
                print(f"❌ Indicator {name} missing fields: {missing_indicator_fields}")
                return False
        
        print("✅ State file structure is valid")
        print(f"📊 File size: {indicator_file.stat().st_size} bytes")
        print(f"📅 Last modified: {datetime.fromtimestamp(indicator_file.stat().st_mtime)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in state file content test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run all persistent indicator state tests."""
    print("🚀 Persistent Indicator State Tests")
    print("=" * 60)
    
    # Test 1: Persistent file creation
    test1_result = test_persistent_indicator_file()
    
    # Test 2: Browser refresh simulation
    test2_result = test_browser_refresh_simulation()
    
    # Test 3: State file content validation
    test3_result = test_state_file_content()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"  1. Persistent File Creation: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"  2. Browser Refresh Simulation: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"  3. State File Content: {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    overall_success = test1_result and test2_result and test3_result
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 Persistent indicator state is working correctly!")
        print("📌 Key Features Validated:")
        print("   - Indicators saved to persistent file")
        print("   - Indicators restored after browser refresh")
        print("   - State file structure is valid")
        print("   - Cache integration working")
    else:
        print("\n🔧 Persistent indicator state needs further fixes.")
    
    return overall_success

if __name__ == "__main__":
    main()
