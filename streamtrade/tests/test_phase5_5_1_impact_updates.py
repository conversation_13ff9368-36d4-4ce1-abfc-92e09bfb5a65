"""
Test suite for Phase 5.5.1: Impact-Based Update System.
Tests impact analysis, selective calculation, and performance optimization.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.cache.impact_manager import ImpactManager, ChangeType, ChangeEvent
from streamtrade.indicators.indicator_manager import IndicatorManager
from streamtrade.visualization.chart_viewer import <PERSON><PERSON>iewer
from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)


class TestImpactManager(unittest.TestCase):
    """Test Impact Manager functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.impact_manager = ImpactManager()
        
        # Create sample indicator configuration
        self.sample_indicators = {
            'SMA_20': {'type': 'SMA', 'params': {'period': 20}},
            'RSI_14': {'type': 'RSI', 'params': {'period': 14}},
            'MACD': {'type': 'MACD', 'params': {'fast': 12, 'slow': 26, 'signal': 9}}
        }
    
    def test_change_event_creation(self):
        """Test change event creation and registration."""
        change_event = self.impact_manager.register_change(
            change_type=ChangeType.INDICATOR_PARAM,
            affected_indicators={'SMA_20'},
            metadata={'old_period': 20, 'new_period': 50}
        )
        
        self.assertIsInstance(change_event, ChangeEvent)
        self.assertEqual(change_event.change_type, ChangeType.INDICATOR_PARAM)
        self.assertEqual(change_event.affected_indicators, {'SMA_20'})
        self.assertIn('old_period', change_event.metadata)
    
    def test_impact_analysis_data_reload(self):
        """Test impact analysis for data reload."""
        change_event = self.impact_manager.register_change(
            change_type=ChangeType.DATA_RELOAD,
            affected_indicators=set(),
            metadata={}
        )
        
        indicators_to_recalc = self.impact_manager.get_indicators_to_recalculate(
            change_event, self.sample_indicators
        )
        
        # Data reload should affect all indicators
        self.assertEqual(indicators_to_recalc, set(self.sample_indicators.keys()))
    
    def test_impact_analysis_parameter_change(self):
        """Test impact analysis for parameter change."""
        change_event = self.impact_manager.register_change(
            change_type=ChangeType.INDICATOR_PARAM,
            affected_indicators={'SMA_20'},
            metadata={'parameter': 'period', 'old_value': 20, 'new_value': 50}
        )
        
        indicators_to_recalc = self.impact_manager.get_indicators_to_recalculate(
            change_event, self.sample_indicators
        )
        
        # Parameter change should affect only the specific indicator
        self.assertEqual(indicators_to_recalc, {'SMA_20'})
    
    def test_impact_analysis_indicator_add(self):
        """Test impact analysis for indicator addition."""
        change_event = self.impact_manager.register_change(
            change_type=ChangeType.INDICATOR_ADD,
            affected_indicators={'EMA_50'},
            metadata={'indicator_type': 'EMA', 'parameters': {'period': 50}}
        )
        
        indicators_to_recalc = self.impact_manager.get_indicators_to_recalculate(
            change_event, self.sample_indicators
        )
        
        # Adding indicator should affect only the new indicator (but it's not in current indicators)
        self.assertEqual(indicators_to_recalc, set())
    
    def test_impact_analysis_indicator_remove(self):
        """Test impact analysis for indicator removal."""
        change_event = self.impact_manager.register_change(
            change_type=ChangeType.INDICATOR_REMOVE,
            affected_indicators={'SMA_20'},
            metadata={'indicator_type': 'SMA'}
        )
        
        indicators_to_recalc = self.impact_manager.get_indicators_to_recalculate(
            change_event, self.sample_indicators
        )
        
        # Removing indicator should not affect other indicators
        self.assertEqual(indicators_to_recalc, set())
    
    def test_change_history(self):
        """Test change history tracking."""
        # Register multiple changes
        self.impact_manager.register_change(ChangeType.DATA_RELOAD, set(), {})
        self.impact_manager.register_change(ChangeType.INDICATOR_PARAM, {'SMA_20'}, {})
        self.impact_manager.register_change(ChangeType.INDICATOR_ADD, {'EMA_50'}, {})
        
        history = self.impact_manager.get_change_history(limit=5)
        
        self.assertEqual(len(history), 3)
        self.assertEqual(history[-1].change_type, ChangeType.INDICATOR_ADD)
        self.assertEqual(history[-2].change_type, ChangeType.INDICATOR_PARAM)
        self.assertEqual(history[-3].change_type, ChangeType.DATA_RELOAD)
    
    def test_impact_statistics(self):
        """Test impact statistics generation."""
        # Register some changes
        self.impact_manager.register_change(ChangeType.DATA_RELOAD, set(), {})
        self.impact_manager.register_change(ChangeType.INDICATOR_PARAM, {'SMA_20'}, {})
        self.impact_manager.register_change(ChangeType.INDICATOR_PARAM, {'RSI_14'}, {})
        
        stats = self.impact_manager.get_impact_statistics()
        
        self.assertEqual(stats['total_changes'], 3)
        self.assertEqual(stats['change_types']['indicator_param'], 2)
        self.assertEqual(stats['change_types']['data_reload'], 1)
        self.assertEqual(len(stats['recent_changes']), 3)


class TestIndicatorManagerImpactUpdates(unittest.TestCase):
    """Test Indicator Manager with impact-based updates."""
    
    def setUp(self):
        """Set up test environment."""
        self.indicator_manager = IndicatorManager()
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='1H')
        self.sample_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.5, 2.5, 100),
            'low': np.random.uniform(0.5, 1.5, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        }, index=dates)
        
        # Ensure OHLC logic
        for i in range(len(self.sample_data)):
            high = max(self.sample_data.iloc[i]['open'], self.sample_data.iloc[i]['close'])
            low = min(self.sample_data.iloc[i]['open'], self.sample_data.iloc[i]['close'])
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('high')] = max(high, self.sample_data.iloc[i]['high'])
            self.sample_data.iloc[i, self.sample_data.columns.get_loc('low')] = min(low, self.sample_data.iloc[i]['low'])
    
    def test_add_indicator_impact_registration(self):
        """Test that adding an indicator registers the change."""
        # Clear history
        self.indicator_manager.impact_manager.clear_history()
        
        # Add indicator
        success = self.indicator_manager.add_indicator('SMA_20', 'SMA', {'period': 20})
        self.assertTrue(success)
        
        # Check that change was registered
        history = self.indicator_manager.impact_manager.get_change_history(limit=1)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].change_type, ChangeType.INDICATOR_ADD)
        self.assertEqual(history[0].affected_indicators, {'SMA_20'})
    
    def test_remove_indicator_impact_registration(self):
        """Test that removing an indicator registers the change."""
        # Add indicator first
        self.indicator_manager.add_indicator('SMA_20', 'SMA', {'period': 20})
        
        # Clear history
        self.indicator_manager.impact_manager.clear_history()
        
        # Remove indicator
        success = self.indicator_manager.remove_indicator('SMA_20')
        self.assertTrue(success)
        
        # Check that change was registered
        history = self.indicator_manager.impact_manager.get_change_history(limit=1)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].change_type, ChangeType.INDICATOR_REMOVE)
        self.assertEqual(history[0].affected_indicators, {'SMA_20'})
    
    def test_update_parameters_impact_registration(self):
        """Test that updating parameters registers the change."""
        # Add indicator first
        self.indicator_manager.add_indicator('SMA_20', 'SMA', {'period': 20})
        
        # Clear history
        self.indicator_manager.impact_manager.clear_history()
        
        # Update parameters
        success = self.indicator_manager.update_indicator_parameters('SMA_20', {'period': 50})
        self.assertTrue(success)
        
        # Check that change was registered
        history = self.indicator_manager.impact_manager.get_change_history(limit=1)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].change_type, ChangeType.INDICATOR_PARAM)
        self.assertEqual(history[0].affected_indicators, {'SMA_20'})
    
    def test_selective_calculation(self):
        """Test selective indicator calculation."""
        # Add multiple indicators
        self.indicator_manager.add_indicator('SMA_20', 'SMA', {'period': 20})
        self.indicator_manager.add_indicator('RSI_14', 'RSI', {'period': 14})
        self.indicator_manager.add_indicator('EMA_50', 'EMA', {'period': 50})
        
        # Calculate only specific indicators
        results = self.indicator_manager.calculate_selective(
            self.sample_data, {'SMA_20', 'RSI_14'}
        )
        
        # Should only have results for the specified indicators
        self.assertEqual(set(results.keys()), {'SMA_20', 'RSI_14'})
        self.assertNotIn('EMA_50', results)
    
    def test_impact_based_calculation(self):
        """Test impact-based calculation workflow."""
        # Add multiple indicators
        self.indicator_manager.add_indicator('SMA_20', 'SMA', {'period': 20})
        self.indicator_manager.add_indicator('RSI_14', 'RSI', {'period': 14})
        self.indicator_manager.add_indicator('EMA_50', 'EMA', {'period': 50})
        
        # Test parameter change impact
        results = self.indicator_manager.calculate_with_impact_analysis(
            self.sample_data,
            change_type=ChangeType.INDICATOR_PARAM,
            affected_indicators={'SMA_20'}
        )
        
        # Should recalculate only the affected indicator
        # Note: The method might return all indicators if it falls back to calculate_all
        self.assertIn('SMA_20', results)


if __name__ == '__main__':
    # Configure logging for tests
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    unittest.main(verbosity=2)
