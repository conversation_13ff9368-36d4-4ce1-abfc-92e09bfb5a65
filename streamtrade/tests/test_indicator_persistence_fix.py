#!/usr/bin/env python3
"""
Test script for Indicator Persistence and Overlay Fix
Tests if indicators persist after refresh and display correctly as overlays.
"""

import sys
import os
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)

def test_indicator_metadata():
    """Test if cached indicators have proper metadata for overlay display."""
    print("🧪 Testing Indicator Metadata...")
    
    try:
        # Initialize chart viewer
        chart_viewer = ChartViewer()
        
        # Load some data
        print("📊 Loading data...")
        success = chart_viewer.load_data_n_days_back("EURUSD", "H1", 3)
        if not success:
            print("❌ Failed to load data")
            return False
        
        print(f"✅ Loaded {len(chart_viewer.current_data)} candles")
        
        # Add overlay indicators
        print("\n📈 Adding overlay indicators...")
        
        # Add SMA indicator (should be overlay)
        sma_success = chart_viewer.add_indicator(
            name="SMA_20",
            indicator_type="SMA",
            parameters={"period": 20}
        )
        
        # Add EMA indicator (should be overlay)
        ema_success = chart_viewer.add_indicator(
            name="EMA_50",
            indicator_type="EMA", 
            parameters={"period": 50}
        )
        
        print(f"SMA added: {sma_success}")
        print(f"EMA added: {ema_success}")
        
        # Check indicator metadata
        print(f"\n🔍 Checking indicator metadata:")
        for name, result in chart_viewer.current_indicators.items():
            print(f"  - {name}:")
            print(f"    Category: {result.metadata.get('category', 'UNKNOWN')}")
            print(f"    Description: {result.metadata.get('description', 'UNKNOWN')}")
            print(f"    From cache: {result.metadata.get('from_cache', False)}")
            print(f"    Data series: {list(result.data.keys())}")
        
        # Test cache loading with new instance
        print("\n🔄 Testing cache loading with new instance...")
        chart_viewer2 = ChartViewer()
        
        # Load same data
        success2 = chart_viewer2.load_data_n_days_back("EURUSD", "H1", 3)
        if not success2:
            print("❌ Failed to load data in second instance")
            return False
        
        # Add same indicators
        sma_success2 = chart_viewer2.add_indicator(
            name="SMA_20",
            indicator_type="SMA",
            parameters={"period": 20}
        )
        
        print(f"SMA added in second instance: {sma_success2}")
        
        # Check if metadata is preserved
        if "SMA_20" in chart_viewer2.current_indicators:
            result = chart_viewer2.current_indicators["SMA_20"]
            from_cache = result.metadata.get('from_cache', False)
            category = result.metadata.get('category', 'UNKNOWN')
            description = result.metadata.get('description', 'UNKNOWN')
            
            print(f"SMA metadata from cache:")
            print(f"  - From cache: {from_cache}")
            print(f"  - Category: {category}")
            print(f"  - Description: {description}")
            
            if from_cache and category != 'UNKNOWN':
                print("✅ Indicator metadata preserved in cache!")
                return True
            else:
                print("⚠️ Indicator metadata not properly preserved")
                return False
        else:
            print("❌ SMA indicator not found in second instance")
            return False
            
    except Exception as e:
        logger.error(f"Error in indicator metadata test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def test_session_state_persistence():
    """Test session state persistence simulation."""
    print("\n🧪 Testing Session State Persistence...")
    
    try:
        # Simulate session state
        mock_session_state = {}
        
        # Initialize chart viewer
        chart_viewer = ChartViewer()
        
        # Load data
        success = chart_viewer.load_data_n_days_back("EURUSD", "H1", 3)
        if not success:
            print("❌ Failed to load data")
            return False
        
        # Add indicators
        chart_viewer.add_indicator("SMA_20", "SMA", {"period": 20})
        chart_viewer.add_indicator("EMA_50", "EMA", {"period": 50})
        
        # Manually save state (simulating what save_indicator_state does)
        mock_session_state['chart_indicators'] = {
            'indicators': {name: {
                'indicator_type': config.indicator_type,
                'parameters': config.parameters,
                'enabled': config.enabled,
                'display_name': config.display_name
            } for name, config in chart_viewer.indicator_manager.indicators.items()},
            'pair': chart_viewer.current_pair,
            'timeframe': chart_viewer.current_timeframe
        }
        
        print(f"Saved indicators to mock session state: {len(mock_session_state['chart_indicators']['indicators'])}")
        
        # Create new chart viewer (simulating refresh)
        chart_viewer2 = ChartViewer()
        success2 = chart_viewer2.load_data_n_days_back("EURUSD", "H1", 3)
        
        # Manually restore state (simulating what load_indicator_state does)
        saved_state = mock_session_state['chart_indicators']
        if (saved_state.get('pair') == chart_viewer2.current_pair and 
            saved_state.get('timeframe') == chart_viewer2.current_timeframe):
            
            indicators_data = saved_state.get('indicators', {})
            restored_count = 0
            
            for name, config in indicators_data.items():
                success = chart_viewer2.indicator_manager.add_indicator(
                    name=name,
                    indicator_type=config['indicator_type'],
                    parameters=config['parameters'],
                    display_name=config.get('display_name')
                )
                if success:
                    restored_count += 1
            
            # Recalculate indicators
            chart_viewer2._calculate_indicators()
            
            print(f"Restored {restored_count} indicators")
            print(f"Active indicators: {len(chart_viewer2.current_indicators)}")
            
            if restored_count > 0 and len(chart_viewer2.current_indicators) > 0:
                print("✅ Session state persistence working!")
                return True
            else:
                print("⚠️ Session state persistence not working properly")
                return False
        else:
            print("❌ Pair/timeframe mismatch in session state")
            return False
            
    except Exception as e:
        logger.error(f"Error in session state test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run all indicator persistence tests."""
    print("🚀 Indicator Persistence and Overlay Fix Tests")
    print("=" * 60)
    
    # Test 1: Indicator metadata preservation
    test1_result = test_indicator_metadata()
    
    # Test 2: Session state persistence
    test2_result = test_session_state_persistence()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"  1. Indicator Metadata: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"  2. Session State Persistence: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    overall_success = test1_result and test2_result
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 Indicator persistence and overlay fixes are working correctly!")
        print("📌 Indicators should now:")
        print("   - Persist after browser refresh")
        print("   - Display as overlays (not subplots)")
        print("   - Load from cache with proper metadata")
    else:
        print("\n🔧 Indicator persistence needs further fixes.")
    
    return overall_success

if __name__ == "__main__":
    main()
