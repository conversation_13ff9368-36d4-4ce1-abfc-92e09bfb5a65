"""
Demo script to test settings table styling in different themes.
This script creates a simple HTML file to visually test the theme-aware CSS.
"""

import os
from pathlib import Path


def create_theme_demo():
    """Create HTML demo file to test settings table styling."""
    
    # CSS from the actual implementation
    css_content = """
<style>
.settings-table {
    width: 100%;
    font-size: 12px;
    border-collapse: collapse;
    color: var(--text-color);
    margin: 20px 0;
}
.settings-table td {
    padding: 2px 4px;
    border-bottom: 1px solid var(--border-color, rgba(128, 128, 128, 0.3));
}
.settings-table .setting-name {
    font-weight: bold;
    width: 60%;
    color: var(--text-color);
}
.settings-table .setting-value {
    font-family: monospace;
    background-color: var(--background-color-secondary, rgba(128, 128, 128, 0.1));
    color: var(--text-color);
    padding: 1px 4px;
    border-radius: 3px;
    width: 40%;
    border: 1px solid var(--border-color, rgba(128, 128, 128, 0.2));
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    .settings-table {
        color: #ffffff;
    }
    .settings-table .setting-name {
        color: #ffffff;
    }
    .settings-table .setting-value {
        background-color: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .settings-table td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
}

/* Light theme support */
@media (prefers-color-scheme: light) {
    body {
        background-color: #ffffff;
        color: #000000;
    }
    .settings-table {
        color: #000000;
    }
    .settings-table .setting-name {
        color: #000000;
    }
    .settings-table .setting-value {
        background-color: rgba(0, 0, 0, 0.05);
        color: #000000;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }
    .settings-table td {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
}

/* Manual theme toggle classes for testing */
.light-theme {
    background-color: #ffffff;
    color: #000000;
}
.light-theme .settings-table {
    color: #000000;
}
.light-theme .settings-table .setting-name {
    color: #000000;
}
.light-theme .settings-table .setting-value {
    background-color: rgba(0, 0, 0, 0.05);
    color: #000000;
    border: 1px solid rgba(0, 0, 0, 0.1);
}
.light-theme .settings-table td {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-theme {
    background-color: #1a1a1a;
    color: #ffffff;
}
.dark-theme .settings-table {
    color: #ffffff;
}
.dark-theme .settings-table .setting-name {
    color: #ffffff;
}
.dark-theme .settings-table .setting-value {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}
.dark-theme .settings-table td {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-toggle {
    margin: 20px 0;
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}
</style>
"""
    
    # HTML content
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings Table Theme Demo</title>
    {css_content}
</head>
<body id="demo-body">
    <h1>Settings Table Theme Demo</h1>
    <p>This demo shows how the settings table appears in different themes.</p>
    
    <button class="theme-toggle" onclick="toggleTheme()">Toggle Theme (Light/Dark)</button>
    
    <h2>Current Settings Table</h2>
    <table class="settings-table">
        <tr><td class="setting-name">Data Timezone</td><td class="setting-value">UTC-5</td></tr>
        <tr><td class="setting-name">Display Timezone</td><td class="setting-value">UTC+7</td></tr>
        <tr><td class="setting-name">Forex Open</td><td class="setting-value">16:00</td></tr>
        <tr><td class="setting-name">Non-Forex Open</td><td class="setting-value">17:00</td></tr>
        <tr><td class="setting-name">Days Back Default</td><td class="setting-value">5</td></tr>
        <tr><td class="setting-name">Max Load Candles</td><td class="setting-value">200,000</td></tr>
        <tr><td class="setting-name">Max Display Candles</td><td class="setting-value">15,000</td></tr>
        <tr><td class="setting-name">Cache Size</td><td class="setting-value">10 GB</td></tr>
        <tr><td class="setting-name">Cache All TF</td><td class="setting-value">False</td></tr>
        <tr><td class="setting-name">Default Timeframe</td><td class="setting-value">H1</td></tr>
        <tr><td class="setting-name">Chart Style</td><td class="setting-value">candlestick</td></tr>
        <tr><td class="setting-name">Remove Gaps</td><td class="setting-value">True</td></tr>
        <tr><td class="setting-name">Crosshair Enabled</td><td class="setting-value">True</td></tr>
        <tr><td class="setting-name">Disk Cache</td><td class="setting-value">True</td></tr>
        <tr><td class="setting-name">Cache Compression</td><td class="setting-value">lz4</td></tr>
    </table>
    
    <h3>Instructions:</h3>
    <ul>
        <li>Use the "Toggle Theme" button to switch between light and dark themes</li>
        <li>The table should be readable in both themes</li>
        <li>Text should have good contrast against the background</li>
        <li>Your browser's theme preference will also affect the display</li>
    </ul>
    
    <script>
        let isDark = false;
        
        function toggleTheme() {{
            const body = document.getElementById('demo-body');
            if (isDark) {{
                body.className = 'light-theme';
                isDark = false;
            }} else {{
                body.className = 'dark-theme';
                isDark = true;
            }}
        }}
        
        // Initialize with light theme
        document.getElementById('demo-body').className = 'light-theme';
    </script>
</body>
</html>
"""
    
    # Create demo file
    demo_dir = Path(__file__).parent
    demo_file = demo_dir / "settings_table_theme_demo.html"
    
    with open(demo_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Theme demo created: {demo_file}")
    print(f"📂 Open this file in your browser to test the styling")
    print(f"🎨 Use the toggle button to switch between themes")
    
    return demo_file


if __name__ == "__main__":
    demo_file = create_theme_demo()
    print(f"\n🚀 Demo file ready: {demo_file.absolute()}")
