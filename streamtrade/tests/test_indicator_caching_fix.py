#!/usr/bin/env python3
"""
Test script for Phase 5.5.6: Indicator Cache Integration Fix
Tests if indicators are actually being cached and persisted.
"""

import sys
import os
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)

def test_indicator_caching():
    """Test indicator caching functionality."""
    print("🧪 Testing Indicator Caching Integration...")
    
    try:
        # Initialize chart viewer
        chart_viewer = ChartViewer()
        
        # Load some data
        print("📊 Loading data...")
        success = chart_viewer.load_data_n_days_back("EURUSD", "H1", 3)
        if not success:
            print("❌ Failed to load data")
            return False
        
        print(f"✅ Loaded {len(chart_viewer.current_data)} candles")
        print(f"📋 Data cache key: {chart_viewer.current_data_cache_key}")
        
        # Add some indicators
        print("\n📈 Adding indicators...")
        
        # Add SMA indicator
        sma_success = chart_viewer.add_indicator(
            name="SMA_20",
            indicator_type="SMA",
            parameters={"period": 20}
        )
        
        # Add EMA indicator
        ema_success = chart_viewer.add_indicator(
            name="EMA_50",
            indicator_type="EMA", 
            parameters={"period": 50}
        )
        
        print(f"SMA added: {sma_success}")
        print(f"EMA added: {ema_success}")
        
        # Check if indicators were calculated
        print(f"\n📊 Calculated indicators: {len(chart_viewer.current_indicators)}")
        for name, result in chart_viewer.current_indicators.items():
            print(f"  - {name}: {len(result.data)} series")
            if 'from_cache' in result.metadata:
                print(f"    (Loaded from cache: {result.metadata['from_cache']})")
        
        # Check cache directory
        cache_dir = Path("cache/data/indicators")
        if cache_dir.exists():
            cache_files = list(cache_dir.glob("*.parquet"))
            print(f"\n💾 Cache files found: {len(cache_files)}")
            for file in cache_files:
                print(f"  - {file.name}")
        else:
            print(f"\n❌ Cache directory not found: {cache_dir}")
        
        # Test cache loading by creating new chart viewer
        print("\n🔄 Testing cache loading with new instance...")
        chart_viewer2 = ChartViewer()
        
        # Load same data
        success2 = chart_viewer2.load_data_n_days_back("EURUSD", "H1", 3)
        if not success2:
            print("❌ Failed to load data in second instance")
            return False
        
        # Add same indicators
        sma_success2 = chart_viewer2.add_indicator(
            name="SMA_20",
            indicator_type="SMA",
            parameters={"period": 20}
        )
        
        print(f"SMA added in second instance: {sma_success2}")
        
        # Check if loaded from cache
        if "SMA_20" in chart_viewer2.current_indicators:
            result = chart_viewer2.current_indicators["SMA_20"]
            from_cache = result.metadata.get('from_cache', False)
            print(f"SMA loaded from cache: {from_cache}")
            
            if from_cache:
                print("✅ Indicator caching is working!")
                return True
            else:
                print("⚠️ Indicator was recalculated (not loaded from cache)")
                return False
        else:
            print("❌ SMA indicator not found in second instance")
            return False
            
    except Exception as e:
        logger.error(f"Error in indicator caching test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def test_cache_statistics():
    """Test cache statistics functionality."""
    print("\n📊 Testing Cache Statistics...")
    
    try:
        chart_viewer = ChartViewer()
        data_manager = chart_viewer.data_manager
        
        if hasattr(data_manager, 'indicator_cache') and data_manager.indicator_cache:
            stats = data_manager.indicator_cache.get_indicator_stats()
            print(f"Cache statistics: {stats}")
            
            if stats.get('total_indicators', 0) > 0:
                print("✅ Cache statistics working")
                return True
            else:
                print("⚠️ No indicators found in cache statistics")
                return False
        else:
            print("❌ Indicator cache not available")
            return False
            
    except Exception as e:
        logger.error(f"Error in cache statistics test: {e}")
        print(f"❌ Cache statistics test failed: {e}")
        return False

def main():
    """Run all indicator caching tests."""
    print("🚀 Phase 5.5.6: Indicator Cache Integration Fix Tests")
    print("=" * 60)
    
    # Test 1: Basic indicator caching
    test1_result = test_indicator_caching()
    
    # Test 2: Cache statistics
    test2_result = test_cache_statistics()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"  1. Indicator Caching: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"  2. Cache Statistics: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    overall_success = test1_result and test2_result
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 Indicator caching integration is working correctly!")
    else:
        print("\n🔧 Indicator caching needs further fixes.")
    
    return overall_success

if __name__ == "__main__":
    main()
