"""
Test suite for Phase 5.5.4: Cache Coherency Manager System.
Tests coordinated cache operations, performance monitoring, and error recovery.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys
import tempfile
import shutil
import time

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.cache.coherency_manager import (
    CacheCoherencyManager, PerformanceMonitor, CoherencyState, CoherencyOperation
)
from streamtrade.cache.impact_manager import ChangeType, ChangeEvent
from streamtrade.cache.disk_cache import SmartDiskCache
from streamtrade.cache.indicator_cache import IndicatorCache
from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)


class TestPerformanceMonitor(unittest.TestCase):
    """Test Performance Monitor functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.monitor = PerformanceMonitor()
    
    def test_record_operation_time(self):
        """Test recording operation times."""
        # Record some operation times
        self.monitor.record_operation_time('test_operation', 0.1)
        self.monitor.record_operation_time('test_operation', 0.2)
        self.monitor.record_operation_time('test_operation', 0.15)
        
        # Get performance summary
        summary = self.monitor.get_performance_summary()
        
        self.assertIn('operation_performance', summary)
        self.assertIn('test_operation', summary['operation_performance'])
        
        op_perf = summary['operation_performance']['test_operation']
        self.assertEqual(op_perf['sample_count'], 3)
        self.assertAlmostEqual(op_perf['average_ms'], 150.0, places=1)  # (100+200+150)/3 = 150ms
    
    def test_record_cache_hit_rate(self):
        """Test recording cache hit rates."""
        # Record some hit rates
        self.monitor.record_cache_hit_rate('test_cache', 0.8)
        self.monitor.record_cache_hit_rate('test_cache', 0.9)
        self.monitor.record_cache_hit_rate('test_cache', 0.85)
        
        # Get performance summary
        summary = self.monitor.get_performance_summary()
        
        self.assertIn('cache_performance', summary)
        self.assertIn('test_cache', summary['cache_performance'])
        
        cache_perf = summary['cache_performance']['test_cache']
        self.assertEqual(cache_perf['sample_count'], 3)
        self.assertAlmostEqual(cache_perf['average_hit_rate'], 85.0, places=1)  # (80+90+85)/3 = 85%
    
    def test_performance_recommendations(self):
        """Test performance recommendations generation."""
        # Record slow operation
        self.monitor.record_operation_time('slow_operation', 0.6)  # 600ms
        
        # Record low hit rate cache
        self.monitor.record_cache_hit_rate('slow_cache', 0.5)  # 50%
        
        # Get performance summary
        summary = self.monitor.get_performance_summary()
        
        self.assertIn('recommendations', summary)
        recommendations = summary['recommendations']
        
        # Should have recommendations for slow operation and low hit rate
        slow_op_rec = any('slow_operation' in rec for rec in recommendations)
        slow_cache_rec = any('slow_cache' in rec for rec in recommendations)
        
        self.assertTrue(slow_op_rec)
        self.assertTrue(slow_cache_rec)


class TestCacheCoherencyManager(unittest.TestCase):
    """Test Cache Coherency Manager functionality."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for cache
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Initialize cache components
        self.disk_cache = SmartDiskCache(cache_dir=self.temp_dir / 'cache')
        self.indicator_cache = IndicatorCache(self.disk_cache)
        
        # Initialize coherency manager
        self.coherency_manager = CacheCoherencyManager(
            disk_cache=self.disk_cache,
            indicator_cache=self.indicator_cache
        )
    
    def tearDown(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_coordinate_cache_operation(self):
        """Test coordinated cache operation."""
        # Create change event
        change_event = ChangeEvent(
            change_type=ChangeType.DATA_RELOAD,
            timestamp=datetime.now(),
            affected_indicators=set(),
            metadata={'pair': 'EURUSD', 'timeframe': 'H1'}
        )
        
        # Coordinate operation
        operation_id = self.coherency_manager.coordinate_cache_operation(
            operation_type='test_data_reload',
            change_event=change_event,
            metadata={'test': True}
        )
        
        self.assertIsInstance(operation_id, str)
        self.assertTrue(operation_id.startswith('op_'))
        
        # Check operation was recorded
        operation_details = self.coherency_manager.get_operation_details(operation_id)
        self.assertIsNotNone(operation_details)
        self.assertEqual(operation_details['type'], 'test_data_reload')
        self.assertEqual(operation_details['state'], CoherencyState.COHERENT.value)
    
    def test_coherency_status(self):
        """Test getting coherency status."""
        # Perform some operations
        change_event = ChangeEvent(
            change_type=ChangeType.INDICATOR_PARAM,
            timestamp=datetime.now(),
            affected_indicators={'SMA_20'},
            metadata={'parameter': 'period'}
        )
        
        self.coherency_manager.coordinate_cache_operation(
            'test_param_change', change_event
        )
        
        # Get status
        status = self.coherency_manager.get_coherency_status()
        
        self.assertIn('current_state', status)
        self.assertIn('total_operations', status)
        self.assertIn('recent_operations', status)
        self.assertIn('system_statistics', status)
        self.assertIn('performance', status)
        
        self.assertEqual(status['current_state'], CoherencyState.COHERENT.value)
        self.assertGreaterEqual(status['total_operations'], 1)
    
    def test_performance_optimization(self):
        """Test cache performance optimization."""
        # Record some performance data
        self.coherency_manager.performance_monitor.record_operation_time('test_op', 0.1)
        self.coherency_manager.performance_monitor.record_cache_hit_rate('test_cache', 0.8)
        
        # Run optimization
        optimization_results = self.coherency_manager.optimize_cache_performance()
        
        self.assertIn('optimizations_applied', optimization_results)
        self.assertIn('recommendations', optimization_results)
        self.assertIn('performance_before', optimization_results)
        
        # Should be lists/dicts
        self.assertIsInstance(optimization_results['optimizations_applied'], list)
        self.assertIsInstance(optimization_results['recommendations'], list)
        self.assertIsInstance(optimization_results['performance_before'], dict)
    
    def test_error_recovery(self):
        """Test error recovery functionality."""
        # Simulate error state
        self.coherency_manager._current_state = CoherencyState.ERROR
        
        # Attempt recovery
        recovery_success = self.coherency_manager.recover_from_error()
        
        self.assertTrue(recovery_success)
        self.assertEqual(self.coherency_manager._current_state, CoherencyState.COHERENT)
    
    def test_operation_tracking(self):
        """Test operation tracking and details."""
        # Create multiple operations
        operations = []
        for i in range(3):
            change_event = ChangeEvent(
                change_type=ChangeType.INDICATOR_PARAM,
                timestamp=datetime.now(),
                affected_indicators={f'indicator_{i}'},
                metadata={'test_id': i}
            )
            
            op_id = self.coherency_manager.coordinate_cache_operation(
                f'test_operation_{i}', change_event
            )
            operations.append(op_id)
        
        # Check all operations are tracked
        for op_id in operations:
            details = self.coherency_manager.get_operation_details(op_id)
            self.assertIsNotNone(details)
            self.assertIn('operation_id', details)
            self.assertIn('type', details)
            self.assertIn('state', details)
            self.assertIn('duration_seconds', details)


class TestEnhancedDataManagerIntegration(unittest.TestCase):
    """Test Enhanced Data Manager integration with coherency manager."""
    
    def setUp(self):
        """Set up test environment."""
        # Note: This test uses the actual EnhancedDataManager
        # In a production environment, you might want to mock dependencies
        pass
    
    def test_coordinated_methods_exist(self):
        """Test that coordinated cache methods exist in EnhancedDataManager."""
        # Check if methods exist (without actually calling them to avoid dependencies)
        self.assertTrue(hasattr(EnhancedDataManager, 'coordinate_data_reload'))
        self.assertTrue(hasattr(EnhancedDataManager, 'coordinate_timeframe_switch'))
        self.assertTrue(hasattr(EnhancedDataManager, 'get_cache_coherency_status'))
        self.assertTrue(hasattr(EnhancedDataManager, 'optimize_cache_performance'))
        self.assertTrue(hasattr(EnhancedDataManager, 'recover_cache_coherency'))


class TestChartViewerIntegration(unittest.TestCase):
    """Test Chart Viewer integration with coherency manager."""
    
    def setUp(self):
        """Set up test environment."""
        # Note: This test uses the actual ChartViewer
        # In a production environment, you might want to mock dependencies
        pass
    
    def test_coordinated_methods_exist(self):
        """Test that coordinated cache methods exist in ChartViewer."""
        # Check if methods exist (without actually calling them to avoid dependencies)
        self.assertTrue(hasattr(ChartViewer, 'load_data_coordinated'))
        self.assertTrue(hasattr(ChartViewer, 'switch_timeframe_coordinated'))
        self.assertTrue(hasattr(ChartViewer, 'get_cache_performance_status'))
        self.assertTrue(hasattr(ChartViewer, 'optimize_chart_performance'))


class TestCoherencyOperation(unittest.TestCase):
    """Test CoherencyOperation dataclass."""
    
    def test_coherency_operation_creation(self):
        """Test creating CoherencyOperation."""
        operation = CoherencyOperation(
            operation_id='test_op_1',
            operation_type='test_operation',
            start_time=datetime.now(),
            end_time=None,
            state=CoherencyState.UPDATING,
            affected_systems=['impact_analysis', 'cache_invalidation'],
            metadata={'test': True}
        )
        
        self.assertEqual(operation.operation_id, 'test_op_1')
        self.assertEqual(operation.operation_type, 'test_operation')
        self.assertEqual(operation.state, CoherencyState.UPDATING)
        self.assertEqual(operation.affected_systems, {'impact_analysis', 'cache_invalidation'})
        self.assertEqual(operation.metadata, {'test': True})
        self.assertIsNone(operation.error_message)


if __name__ == '__main__':
    # Configure logging for tests
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    unittest.main(verbosity=2)
