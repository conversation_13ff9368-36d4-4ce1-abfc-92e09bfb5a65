"""
Test suite for Phase 5.5.2: Smart Cache Invalidation System.
Tests smart invalidation, scope analysis, and performance optimization.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys
import tempfile
import shutil

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.cache.invalidation_manager import (
    SmartInvalidationManager, InvalidationAnalyzer, InvalidationScope, InvalidationPlan
)
from streamtrade.cache.impact_manager import ChangeType, ChangeEvent
from streamtrade.cache.disk_cache import SmartDiskCache
from streamtrade.cache.indicator_cache import IndicatorCache
from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)


class TestInvalidationAnalyzer(unittest.TestCase):
    """Test Invalidation Analyzer functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.analyzer = InvalidationAnalyzer()
        
        # Sample cache state
        self.sample_cache_state = {
            'indicators': {
                'SMA_20': {'type': 'SMA', 'params': {'period': 20}},
                'RSI_14': {'type': 'RSI', 'params': {'period': 14}},
                'MACD': {'type': 'MACD', 'params': {'fast': 12, 'slow': 26}}
            },
            'data_keys': ['EURUSD_H1_2023_data', 'GBPUSD_H1_2023_data'],
            'style_keys': ['SMA_20_style', 'RSI_14_style'],
            'cached_timeframes': {'H1', 'H4', 'D1'}
        }
    
    def test_data_reload_invalidation_plan(self):
        """Test invalidation plan for data reload."""
        change_event = ChangeEvent(
            change_type=ChangeType.DATA_RELOAD,
            timestamp=datetime.now(),
            affected_indicators=set(),
            metadata={'pair': 'EURUSD', 'timeframe': 'H1'}
        )
        
        plan = self.analyzer.analyze_invalidation_needs(change_event, self.sample_cache_state)
        
        self.assertEqual(plan.scope, InvalidationScope.DATA_AND_INDICATORS)
        self.assertEqual(plan.affected_indicators, set(self.sample_cache_state['indicators'].keys()))
        self.assertEqual(plan.affected_data_keys, set(self.sample_cache_state['data_keys']))
        self.assertIn("Data reload", plan.reason)
    
    def test_parameter_change_invalidation_plan(self):
        """Test invalidation plan for parameter change."""
        change_event = ChangeEvent(
            change_type=ChangeType.INDICATOR_PARAM,
            timestamp=datetime.now(),
            affected_indicators={'SMA_20'},
            metadata={'parameter': 'period', 'old_value': 20, 'new_value': 50}
        )
        
        plan = self.analyzer.analyze_invalidation_needs(change_event, self.sample_cache_state)
        
        self.assertEqual(plan.scope, InvalidationScope.SINGLE_INDICATOR)
        self.assertEqual(plan.affected_indicators, {'SMA_20'})
        self.assertEqual(plan.affected_data_keys, set())  # Data preserved
        self.assertEqual(plan.affected_style_keys, {'SMA_20'})  # Style invalidated
        self.assertIn("Parameter change", plan.reason)
    
    def test_timeframe_switch_with_cache(self):
        """Test invalidation plan for timeframe switch with cached data."""
        change_event = ChangeEvent(
            change_type=ChangeType.TIMEFRAME_SWITCH,
            timestamp=datetime.now(),
            affected_indicators=set(),
            metadata={'old_timeframe': 'H1', 'new_timeframe': 'H4'}  # H4 is cached
        )
        
        plan = self.analyzer.analyze_invalidation_needs(change_event, self.sample_cache_state)
        
        self.assertEqual(plan.scope, InvalidationScope.ALL_INDICATORS)
        self.assertEqual(plan.affected_indicators, set(self.sample_cache_state['indicators'].keys()))
        self.assertEqual(plan.affected_data_keys, set())  # Data preserved (cached)
        self.assertIn("with cached data", plan.reason)
    
    def test_timeframe_switch_without_cache(self):
        """Test invalidation plan for timeframe switch without cached data."""
        change_event = ChangeEvent(
            change_type=ChangeType.TIMEFRAME_SWITCH,
            timestamp=datetime.now(),
            affected_indicators=set(),
            metadata={'old_timeframe': 'H1', 'new_timeframe': 'M5'}  # M5 not cached
        )
        
        plan = self.analyzer.analyze_invalidation_needs(change_event, self.sample_cache_state)
        
        self.assertEqual(plan.scope, InvalidationScope.DATA_AND_INDICATORS)
        self.assertEqual(plan.affected_indicators, set(self.sample_cache_state['indicators'].keys()))
        self.assertEqual(plan.affected_data_keys, set(self.sample_cache_state['data_keys']))
        self.assertIn("without cached data", plan.reason)
    
    def test_indicator_add_invalidation_plan(self):
        """Test invalidation plan for indicator addition."""
        change_event = ChangeEvent(
            change_type=ChangeType.INDICATOR_ADD,
            timestamp=datetime.now(),
            affected_indicators={'EMA_50'},
            metadata={'indicator_type': 'EMA', 'parameters': {'period': 50}}
        )
        
        plan = self.analyzer.analyze_invalidation_needs(change_event, self.sample_cache_state)
        
        self.assertEqual(plan.scope, InvalidationScope.NONE)
        self.assertEqual(plan.affected_indicators, set())
        self.assertEqual(plan.affected_data_keys, set())
        self.assertIn("doesn't invalidate existing", plan.reason)
    
    def test_indicator_remove_invalidation_plan(self):
        """Test invalidation plan for indicator removal."""
        change_event = ChangeEvent(
            change_type=ChangeType.INDICATOR_REMOVE,
            timestamp=datetime.now(),
            affected_indicators={'SMA_20'},
            metadata={'indicator_type': 'SMA'}
        )
        
        plan = self.analyzer.analyze_invalidation_needs(change_event, self.sample_cache_state)
        
        self.assertEqual(plan.scope, InvalidationScope.SINGLE_INDICATOR)
        self.assertEqual(plan.affected_indicators, {'SMA_20'})
        self.assertEqual(plan.affected_style_keys, {'SMA_20'})
        self.assertIn("Remove indicator", plan.reason)
    
    def test_indicator_toggle_enable_invalidation_plan(self):
        """Test invalidation plan for enabling indicator."""
        change_event = ChangeEvent(
            change_type=ChangeType.INDICATOR_TOGGLE,
            timestamp=datetime.now(),
            affected_indicators={'RSI_14'},
            metadata={'enabled': True}
        )
        
        plan = self.analyzer.analyze_invalidation_needs(change_event, self.sample_cache_state)
        
        self.assertEqual(plan.scope, InvalidationScope.NONE)
        self.assertIn("no cache invalidation needed", plan.reason)
    
    def test_indicator_toggle_disable_invalidation_plan(self):
        """Test invalidation plan for disabling indicator."""
        change_event = ChangeEvent(
            change_type=ChangeType.INDICATOR_TOGGLE,
            timestamp=datetime.now(),
            affected_indicators={'RSI_14'},
            metadata={'enabled': False}
        )
        
        plan = self.analyzer.analyze_invalidation_needs(change_event, self.sample_cache_state)
        
        self.assertEqual(plan.scope, InvalidationScope.NONE)
        self.assertIn("preserving cache", plan.reason)


class TestSmartInvalidationManager(unittest.TestCase):
    """Test Smart Invalidation Manager functionality."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for cache
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Initialize cache components
        self.disk_cache = SmartDiskCache(cache_dir=self.temp_dir / 'cache')
        self.indicator_cache = IndicatorCache(self.disk_cache)
        
        # Initialize invalidation manager
        self.invalidation_manager = SmartInvalidationManager(
            disk_cache=self.disk_cache,
            indicator_cache=self.indicator_cache
        )
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='1h')
        self.sample_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.5, 2.5, 100),
            'low': np.random.uniform(0.5, 1.5, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        }, index=dates)
    
    def tearDown(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_invalidation_execution(self):
        """Test invalidation plan execution."""
        # Create change event
        change_event = ChangeEvent(
            change_type=ChangeType.INDICATOR_PARAM,
            timestamp=datetime.now(),
            affected_indicators={'SMA_20'},
            metadata={'parameter': 'period', 'old_value': 20, 'new_value': 50}
        )
        
        # Execute invalidation
        plan = self.invalidation_manager.invalidate_based_on_change(change_event)
        
        self.assertIsInstance(plan, InvalidationPlan)
        self.assertEqual(plan.scope, InvalidationScope.SINGLE_INDICATOR)
        self.assertEqual(plan.affected_indicators, {'SMA_20'})
    
    def test_invalidation_history_tracking(self):
        """Test invalidation history tracking."""
        # Execute multiple invalidations
        change_events = [
            ChangeEvent(ChangeType.DATA_RELOAD, datetime.now(), set(), {}),
            ChangeEvent(ChangeType.INDICATOR_PARAM, datetime.now(), {'SMA_20'}, {}),
            ChangeEvent(ChangeType.INDICATOR_ADD, datetime.now(), {'EMA_50'}, {})
        ]
        
        for event in change_events:
            self.invalidation_manager.invalidate_based_on_change(event)
        
        # Check history
        stats = self.invalidation_manager.get_invalidation_statistics()
        
        self.assertEqual(stats['total_invalidations'], 3)
        self.assertIn('scope_counts', stats)
        self.assertIn('recent_invalidations', stats)
        self.assertEqual(len(stats['recent_invalidations']), 3)
    
    def test_cache_state_analysis(self):
        """Test cache state analysis."""
        # Get current cache state
        cache_state = self.invalidation_manager._get_current_cache_state()
        
        self.assertIn('indicators', cache_state)
        self.assertIn('data_keys', cache_state)
        self.assertIn('style_keys', cache_state)
        self.assertIn('cached_timeframes', cache_state)
    
    def test_error_handling_fallback(self):
        """Test error handling and fallback behavior."""
        # Create invalid change event
        invalid_event = ChangeEvent(
            change_type=None,  # Invalid change type
            timestamp=datetime.now(),
            affected_indicators=set(),
            metadata={}
        )
        
        # Should not raise exception and should provide fallback
        plan = self.invalidation_manager.invalidate_based_on_change(invalid_event)
        
        self.assertIsInstance(plan, InvalidationPlan)
        # Should fallback to safe invalidation


class TestEnhancedDataManagerIntegration(unittest.TestCase):
    """Test Enhanced Data Manager integration with smart invalidation."""
    
    def setUp(self):
        """Set up test environment."""
        # Note: This test uses the actual EnhancedDataManager
        # In a production environment, you might want to mock dependencies
        pass
    
    def test_smart_invalidation_methods_exist(self):
        """Test that smart invalidation methods exist in EnhancedDataManager."""
        # Check if methods exist (without actually calling them to avoid dependencies)
        self.assertTrue(hasattr(EnhancedDataManager, 'invalidate_cache_smart'))
        self.assertTrue(hasattr(EnhancedDataManager, 'invalidate_on_data_reload'))
        self.assertTrue(hasattr(EnhancedDataManager, 'invalidate_on_timeframe_switch'))
        self.assertTrue(hasattr(EnhancedDataManager, 'get_invalidation_statistics'))


if __name__ == '__main__':
    # Configure logging for tests
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    unittest.main(verbosity=2)
