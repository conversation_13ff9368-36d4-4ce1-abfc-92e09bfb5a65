"""
Debug script to test chart state persistence functionality.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add the project root to the path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.visualization.chart_viewer import <PERSON>Viewer


def debug_state_file():
    """Debug the state file creation and loading."""
    print("🔍 Debugging Chart State Persistence")
    print("=" * 50)
    
    # Check if state file exists
    config_dir = project_root / 'config'
    state_file = config_dir / 'last_chart_state.json'
    
    print(f"📁 Config directory: {config_dir}")
    print(f"📄 State file path: {state_file}")
    print(f"📂 Config dir exists: {config_dir.exists()}")
    print(f"📄 State file exists: {state_file.exists()}")
    
    if state_file.exists():
        print("\n📖 Reading existing state file:")
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            print(f"✅ State loaded successfully:")
            for key, value in state.items():
                print(f"   {key}: {value}")
        except Exception as e:
            print(f"❌ Error reading state file: {e}")
    else:
        print("\n📝 Creating test state file:")
        try:
            config_dir.mkdir(exist_ok=True)
            test_state = {
                'pair': 'EURUSD',
                'timeframe': 'H1',
                'timestamp': datetime.now().isoformat(),
                'data_points': 1000,
                'date_range': {
                    'start': '2024-01-01T00:00:00',
                    'end': '2024-01-05T23:59:59'
                },
                'loading_method': 'test_creation',
                'version': '1.0.0'
            }
            
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(test_state, f, indent=2)
            
            print(f"✅ Test state file created successfully")
            print(f"📄 File size: {state_file.stat().st_size} bytes")
            
        except Exception as e:
            print(f"❌ Error creating state file: {e}")


def debug_cache_system():
    """Debug the cache system for restoration."""
    print("\n🧠 Debugging Cache System")
    print("=" * 50)
    
    try:
        # Initialize data manager
        data_manager = EnhancedDataManager()
        print("✅ EnhancedDataManager initialized")
        
        # Check cache info
        cache_info = data_manager.get_cache_info()
        print(f"📊 Cache info: {cache_info}")
        
        # Test get_cached_data method
        print("\n🔍 Testing get_cached_data method:")
        test_pairs = ['EURUSD', 'GBPUSD', 'USDJPY']
        test_timeframes = ['M1', 'H1', 'H4', 'D1']
        
        for pair in test_pairs:
            for tf in test_timeframes:
                cached_data = data_manager.get_cached_data(pair, tf)
                if cached_data is not None:
                    print(f"✅ Found cached data: {pair} {tf} ({len(cached_data)} candles)")
                else:
                    print(f"❌ No cached data: {pair} {tf}")
        
    except Exception as e:
        print(f"❌ Error in cache system debug: {e}")


def debug_chart_viewer_restoration():
    """Debug chart viewer restoration process."""
    print("\n📈 Debugging Chart Viewer Restoration")
    print("=" * 50)
    
    try:
        # Initialize chart viewer
        chart_viewer = ChartViewer()
        print("✅ ChartViewer initialized")
        
        # Check cache info
        cache_info = chart_viewer.get_cache_info()
        print(f"📊 Chart viewer cache info: {cache_info}")
        
        # Test restoration with sample data
        test_pair = 'EURUSD'
        test_timeframe = 'H1'
        
        print(f"\n🔄 Testing restoration for {test_pair} {test_timeframe}:")
        
        # Check if data manager has get_cached_data method
        if hasattr(chart_viewer.data_manager, 'get_cached_data'):
            print("✅ get_cached_data method exists")
            cached_data = chart_viewer.data_manager.get_cached_data(test_pair, test_timeframe)
            if cached_data is not None:
                print(f"✅ Found cached data: {len(cached_data)} candles")
                
                # Try to set current data
                chart_viewer.current_data = cached_data
                chart_viewer.current_pair = test_pair
                chart_viewer.current_timeframe = test_timeframe
                
                print(f"✅ Set current data in chart viewer")
                print(f"   Pair: {chart_viewer.current_pair}")
                print(f"   Timeframe: {chart_viewer.current_timeframe}")
                print(f"   Data points: {len(chart_viewer.current_data) if chart_viewer.current_data is not None else 0}")
                
            else:
                print(f"❌ No cached data found for {test_pair} {test_timeframe}")
        else:
            print("❌ get_cached_data method not found")
        
    except Exception as e:
        print(f"❌ Error in chart viewer debug: {e}")


def debug_session_state_simulation():
    """Debug session state simulation."""
    print("\n🔄 Debugging Session State Simulation")
    print("=" * 50)
    
    # Simulate the restoration process
    try:
        # Load state file
        config_dir = project_root / 'config'
        state_file = config_dir / 'last_chart_state.json'
        
        if state_file.exists():
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            print(f"✅ Loaded state: {state.get('pair')} {state.get('timeframe')}")
            
            # Simulate session state
            last_selected_pair = state.get('pair')
            last_selected_timeframe = state.get('timeframe')
            
            if last_selected_pair and last_selected_timeframe:
                print(f"✅ Have persistent state to restore")
                
                # Initialize chart viewer
                chart_viewer = ChartViewer()
                
                # Check cache
                cache_info = chart_viewer.get_cache_info()
                has_cache = cache_info.get('disk_cache_entries', 0) > 0 or cache_info.get('memory_cache_entries', 0) > 0
                
                print(f"📊 Cache available: {has_cache}")
                print(f"   Disk cache entries: {cache_info.get('disk_cache_entries', 0)}")
                print(f"   Memory cache entries: {cache_info.get('memory_cache_entries', 0)}")
                
                if has_cache:
                    # Try to get cached data
                    if hasattr(chart_viewer.data_manager, 'get_cached_data'):
                        cached_data = chart_viewer.data_manager.get_cached_data(last_selected_pair, last_selected_timeframe)
                        if cached_data is not None and not cached_data.empty:
                            print(f"✅ Found cached data for restoration: {len(cached_data)} candles")
                            
                            # Simulate restoration
                            chart_viewer.current_data = cached_data
                            chart_viewer.current_pair = last_selected_pair
                            chart_viewer.current_timeframe = last_selected_timeframe
                            
                            print(f"✅ Restoration simulation successful!")
                            return True
                        else:
                            print(f"❌ No cached data found")
                    else:
                        print(f"❌ get_cached_data method not available")
                else:
                    print(f"❌ No cache available")
            else:
                print(f"❌ No persistent state available")
        else:
            print(f"❌ No state file found")
            
    except Exception as e:
        print(f"❌ Error in session state simulation: {e}")
    
    return False


def main():
    """Run all debug tests."""
    print("🚀 Chart State Persistence Debug Tool")
    print("=" * 60)
    
    # Run debug tests
    debug_state_file()
    debug_cache_system()
    debug_chart_viewer_restoration()
    result = debug_session_state_simulation()
    
    print("\n" + "=" * 60)
    if result:
        print("✅ Chart state persistence should work!")
    else:
        print("❌ Chart state persistence has issues that need fixing")
    
    print("🔚 Debug completed")


if __name__ == "__main__":
    main()
