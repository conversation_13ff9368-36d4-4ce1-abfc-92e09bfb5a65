"""
Test script to verify chart state persistence and restoration.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add the project root to the path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def create_test_state():
    """Create a test state file."""
    config_dir = project_root / 'config'
    config_dir.mkdir(exist_ok=True)
    state_file = config_dir / 'last_chart_state.json'
    
    test_state = {
        'pair': 'EURUSD',
        'timeframe': 'H1',
        'timestamp': datetime.now().isoformat(),
        'data_points': 1000,
        'date_range': {
            'start': '2024-01-01T00:00:00',
            'end': '2024-01-05T23:59:59'
        },
        'loading_method': 'test_creation',
        'version': '1.0.0'
    }
    
    with open(state_file, 'w', encoding='utf-8') as f:
        json.dump(test_state, f, indent=2)
    
    print(f"✅ Created test state file: {state_file}")
    print(f"📄 State: {test_state['pair']} {test_state['timeframe']}")
    return state_file

def test_state_loading():
    """Test loading the state file."""
    config_dir = project_root / 'config'
    state_file = config_dir / 'last_chart_state.json'
    
    if state_file.exists():
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if content:
                state = json.loads(content)
                
                # Validate required fields
                if state.get('pair') and state.get('timeframe'):
                    print(f"✅ State loaded successfully: {state.get('pair')} {state.get('timeframe')}")
                    return state
                else:
                    print("❌ Invalid state file: missing required fields")
            else:
                print("❌ Empty state file")
                
        except json.JSONDecodeError as e:
            print(f"❌ Corrupted state file: {e}")
        except Exception as e:
            print(f"❌ Error reading state file: {e}")
    else:
        print("❌ No state file found")
    
    return None

def main():
    """Run the test."""
    print("🧪 Testing Chart State Persistence")
    print("=" * 50)
    
    # Step 1: Create test state
    print("\n1️⃣ Creating test state file...")
    state_file = create_test_state()
    
    # Step 2: Test loading
    print("\n2️⃣ Testing state loading...")
    loaded_state = test_state_loading()
    
    if loaded_state:
        print("\n✅ Chart state persistence is working!")
        print(f"   Pair: {loaded_state['pair']}")
        print(f"   Timeframe: {loaded_state['timeframe']}")
        print(f"   Timestamp: {loaded_state['timestamp']}")
        
        print("\n📝 Instructions for testing:")
        print("1. Start the Streamlit app: streamlit run run_streamlit.py")
        print("2. Load some data (any pair/timeframe)")
        print("3. Refresh the browser")
        print("4. The chart should be restored automatically")
        
    else:
        print("\n❌ Chart state persistence has issues")
    
    print("\n🔚 Test completed")

if __name__ == "__main__":
    main()
